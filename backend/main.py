from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.api import auth
from backend.api import data

app = FastAPI()

# CORS配置，允许本地前端开发环境跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议指定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册auth路由
app.include_router(auth.router)
# 注册data路由
app.include_router(data.router)

@app.get("/")
def root():
    return {"message": "Backend service is running!"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("backend.main:app", host="0.0.0.0", port=8000, reload=True)
