from sqlalchemy import Column, Integer, Float, String, DateTime, Boolean, UniqueConstraint, BigInteger
from sqlalchemy.ext.declarative import declared_attr
from datetime import datetime

from backend.database.connection import Base

class DrawdownTracker1H(Base):
    __tablename__ = 'drawdown_tracker_1h'
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String, nullable=False, index=True)
    price = Column(Float)
    high_365d = Column(Float)
    drawdown_365d = Column(Float)
    high_700d = Column(Float)
    drawdown_700d = Column(Float)
    volume_24h = Column(Float)
    onboard_date = Column(BigInteger, nullable=True)  # 新增上线日期字段，毫秒时间戳
    updated_at = Column(DateTime, default=datetime.utcnow, index=True)


class FundingRate(Base):
    __tablename__ = 'fundingrate'
    id = Column(Integer, primary_key=True, autoincrement=True)
    exchange = Column(String(32), nullable=False)
    symbol = Column(String(64), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    funding_rate = Column(Float, nullable=False)
    __table_args__ = (UniqueConstraint('exchange', 'symbol', 'timestamp', name='uix_fundingrate_all'),)

# Base class for all kline data tables
class KlineBase:
    base_asset = Column(String, nullable=False, index=True)  # 新增 base_asset 字段
    """
    Base class for all kline data tables.
    """
    id = Column(Integer, primary_key=True)
    exchange = Column(String, nullable=False, index=True, default='binance')
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)

    # Add a unique constraint on symbol and timestamp
    @declared_attr
    def __table_args__(cls):
        return (UniqueConstraint('exchange', 'symbol', 'timestamp', name=f'uix_{cls.__tablename__}_exchange_symbol_timestamp'),)

# Raw kline data tables
class RawKline_15M(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """15-minute raw kline data."""
    __tablename__ = 'rawkline_15m'

class RawKline_1H(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """1-hour raw kline data."""
    __tablename__ = 'rawkline_1h'

class RawKline_4H(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """4-hour raw kline data."""
    __tablename__ = 'rawkline_4h'

class RawKline_1D(Base, KlineBase):
    # base_asset 字段已通过 KlineBase 继承，无需重复定义
    """1-day raw kline data."""
    __tablename__ = 'rawkline_1d'

class OpenInterest(Base):
    """Open interest data for any timeframe."""
    __tablename__ = 'openinterest'
    id = Column(Integer, primary_key=True, autoincrement=True)
    exchange = Column(String(32), nullable=False)
    symbol = Column(String(64), nullable=False)
    base_asset = Column(String(32), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    timeframe = Column(String(8), nullable=False)  # e.g. '1h', '15m', '4h', '1d'
    open_interest = Column(Float, nullable=False)
    __table_args__ = (UniqueConstraint('exchange', 'symbol', 'timestamp', 'timeframe', name='uix_openinterest_all'),)

# Trend signal table base class
from sqlalchemy.ext.declarative import declared_attr

class TrendSignalBase:
    @declared_attr
    def __table_args__(cls):
        return (UniqueConstraint('symbol', 'timestamp', name=f'uix_{cls.__tablename__}_symbol_timestamp'),)

# Trend signal tables
class TrendSignal_15M(Base, TrendSignalBase):
    """15-minute trend signal data."""
    __tablename__ = 'trendsignal_15m'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 15分钟趋势信号字段
    consecutive_ups_count = Column(BigInteger)  # 连续上涨次数，允许为正/负
    total_consecutive_downs = Column(BigInteger)  # 累计出现过的连续下跌段数
    consecutive_3_and_1h_1pct_count = Column(Float)
    rank_in_1h_1pct = Column(BigInteger)
    rank_15m = Column(Float)
    rank_1h = Column(Float)
    rank_3h = Column(Float)
    avg_increase_30m = Column(Float)
    avg_increase_1h = Column(Float)
    avg_increase_4h = Column(Float)
    avg_increase_12h = Column(Float)
    amplitude_15m = Column(Float, name="15m_amplitude")
    drawdown_1h = Column(Float, name="1h_drawdown")
    increase_1h = Column(Float)
    increase_3h = Column(Float)
    increase_4h = Column(Float)
    increase_6h = Column(Float)
    increase_12h = Column(Float)
    k1_increase = Column(Float)
    k2_increase = Column(Float)
    k3_increase = Column(Float)
    volume_24h = Column(Float)
    close_low_ratio = Column(Float)
    drawdown_from_365d_high = Column(Float)

class TrendSignal_1H(Base, TrendSignalBase):
    """1-hour trend signal data."""
    __tablename__ = 'trendsignal_1h'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 1小时趋势信号字段
    consecutive_ups_count = Column(BigInteger)  # 连续上涨次数，允许为正/负
    total_consecutive_downs = Column(BigInteger)  # 累计出现过的连续下跌段数
    consecutive_3_and_3h_1pct_count = Column(Float)
    rank_in_3h_1pct = Column(BigInteger)
    rank_1h_market = Column(Float, nullable=True)
    rank_3h_market = Column(Float, nullable=True)
    rank_4h_market = Column(Float, nullable=True)
    rank_6h_market = Column(Float, nullable=True)
    avg_increase_1h = Column(Float)
    avg_increase_4h = Column(Float)
    avg_increase_12h = Column(Float)
    amplitude_1h = Column(Float, name="1h_amplitude")
    drawdown_1h = Column(Float, name="1h_drawdown")
    increase_1h = Column(Float)
    increase_3h = Column(Float)
    increase_4h = Column(Float)
    increase_6h = Column(Float)
    increase_12h = Column(Float)
    k1_increase = Column(Float)
    k2_increase = Column(Float)
    k3_increase = Column(Float)
    volume_24h = Column(Float)
    close_low_ratio = Column(Float)
    up_pairs_ratio = Column(Float)

class TrendSignal_4H(Base, TrendSignalBase):
    """4-hour trend signal data."""
    __tablename__ = 'trendsignal_4h'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 4小时趋势信号字段
    consecutive_ups_count = Column(BigInteger)  # 连续上涨次数，允许为正/负
    total_consecutive_downs = Column(BigInteger)  # 累计出现过的连续下跌段数
    consecutive_3_and_12h_1pct_count = Column(Float)
    rank_in_12h_1pct = Column(BigInteger)
    rank_4h_market = Column(Float)
    rank_12h_market = Column(Float)
    rank_24h_market = Column(Float)
    rank_48h_market = Column(Float)
    avg_increase_4h = Column(Float)
    avg_increase_12h = Column(Float)
    amplitude_4h = Column(Float, name="4h_amplitude")
    drawdown_4h = Column(Float, name="4h_drawdown")
    increase_4h = Column(Float)
    increase_12h = Column(Float)
    increase_24h = Column(Float)
    increase_48h = Column(Float)
    k1_increase = Column(Float)
    k2_increase = Column(Float)
    k3_increase = Column(Float)
    volume_24h = Column(Float)
    close_low_ratio = Column(Float)
    up_pairs_ratio = Column(Float)

class TrendSignal_1D(Base, TrendSignalBase):
    """1-day trend signal data."""
    __tablename__ = 'trendsignal_1d'

    # 基本字段
    id = Column(Integer, primary_key=True)
    symbol = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # 1天趋势信号字段
    consecutive_ups_count = Column(BigInteger)  # 连续上涨次数，允许为正/负
    total_consecutive_downs = Column(BigInteger)  # 累计出现过的连续下跌段数
    consecutive_3_and_3d_1pct_count = Column(Float)
    rank_in_3d_1pct = Column(BigInteger)
    rank_1d_market = Column(Float)
    rank_3d_market = Column(Float)
    rank_4d_market = Column(Float)
    rank_7d_market = Column(Float)
    avg_increase_1d = Column(Float)
    avg_increase_4d = Column(Float)
    avg_increase_14d = Column(Float)
    amplitude_1d = Column(Float, name="1d_amplitude")
    drawdown_1d = Column(Float, name="1d_drawdown")
    increase_1d = Column(Float)
    increase_3d = Column(Float)
    increase_4d = Column(Float)
    increase_7d = Column(Float)
    increase_14d = Column(Float)
    k1_increase = Column(Float)
    k2_increase = Column(Float)
    k3_increase = Column(Float)
    volume_24h = Column(Float)
    close_low_ratio = Column(Float)
    up_pairs_ratio = Column(Float)
    consecutive_downs = Column(BigInteger)


class CoinMarketInfo(Base):
    """币种市值等信息，来源于 CoinGecko /coins/markets 接口"""
    __tablename__ = 'coin_market_info'

    id = Column(String, primary_key=True)  # CoinGecko id
    symbol = Column(String, nullable=False, index=True)
    name = Column(String, nullable=False)
    image = Column(String)
    market_cap = Column(Float)
    market_cap_rank = Column(Integer)
    circulating_supply = Column(Float)
    total_supply = Column(Float)
    current_price = Column(Float)
    price_change_percentage_24h = Column(Float)
    last_updated = Column(DateTime)
    # 可根据实际需要扩展更多字段


class BinanceFuturesPairInfo(Base):
    """币安合约交易对信息，包括上线时间等"""
    __tablename__ = 'binance_futures_pair_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String, nullable=False, index=True, unique=True)  # 交易对如BTCUSDT
    base_asset = Column(String, nullable=False, index=True)
    quote_asset = Column(String, nullable=False, index=True)
    onboard_date = Column(BigInteger, nullable=False)  # 上线时间（毫秒时间戳）
    pair_type = Column(String, nullable=True)  # 如PERPETUAL等
    status = Column(String, nullable=True)
    # 可扩展更多币安原生字段


class MergedPairMarketInfo(Base):
    """合并后的有市值信息的交易对（币安合约+CoinGecko市值）"""
    __tablename__ = 'merged_pair_market_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String, nullable=False, index=True, unique=True)
    base_asset = Column(String, nullable=False, index=True)
    quote_asset = Column(String, nullable=False, index=True)
    onboard_date = Column(BigInteger, nullable=False)
    coingecko_id = Column(String, nullable=True, index=True)
    name = Column(String, nullable=True)
    market_cap = Column(Float)
    market_cap_rank = Column(Integer)
    circulating_supply = Column(Float)
    total_supply = Column(Float)
    current_price = Column(Float)
    price_change_percentage_24h = Column(Float)
    last_updated = Column(DateTime)
    # 可根据实际需要扩展更多字段

