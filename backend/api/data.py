from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from backend.database.connection import get_session
from backend.database.models import DrawdownTracker1H, TrendSignal_1D, TrendSignal_1H, TrendSignal_4H, TrendSignal_15M
from typing import List, Optional, Dict, Any
import requests
import time
from datetime import datetime, timedelta


router = APIRouter(prefix="/data", tags=["data"])

def get_db():
    db = get_session()
    try:
        yield db
    finally:
        db.close()

# 1. 跌幅追踪列表接口
@router.get("/drawdown_tracker_1h")
def get_drawdown_list_1h(
    db: Session = Depends(get_db),
    symbol: Optional[str] = Query(None),
    order_by: Optional[str] = Query("drawdown_365d"),
    desc: bool = Query(True),
    skip: int = Query(0),
    limit: int = Query(50),
    onboard_date_from: Optional[int] = Query(None, description="上线时间起（毫秒时间戳）"),
    onboard_date_to: Optional[int] = Query(None, description="上线时间止（毫秒时间戳）"),
    minDrawdown: Optional[float] = Query(None, alias="min_drawdown", description="跌幅筛选，<=该值（负数）")
):
    q = db.query(DrawdownTracker1H)
    if symbol:
        q = q.filter(DrawdownTracker1H.symbol == symbol)
    if onboard_date_from is not None:
        q = q.filter(DrawdownTracker1H.onboard_date >= onboard_date_from)
    if onboard_date_to is not None:
        q = q.filter(DrawdownTracker1H.onboard_date <= onboard_date_to)
    if minDrawdown is not None:
        q = q.filter(DrawdownTracker1H.drawdown_365d <= minDrawdown)
    total = q.count()
    if order_by and hasattr(DrawdownTracker1H, order_by):
        col = getattr(DrawdownTracker1H, order_by)
        q = q.order_by(col.desc() if desc else col.asc())
    q = q.offset(skip).limit(limit)
    return {
        "data": [row.__dict__ for row in q.all()],
        "total": total
    }

# 2. 趋势信号通用查询
@router.get("/trendsignal/{timeframe}")
def get_trendsignal_list(
    timeframe: str,
    db: Session = Depends(get_db),
    symbol: Optional[str] = Query(None),
    min_consecutive: Optional[int] = Query(None),
    min_consecutive_op: Optional[str] = Query('>='),
    min_total: Optional[int] = Query(None),
    min_increase_4h: Optional[float] = Query(None),
    min_volume_24h: Optional[float] = Query(None),
    date: Optional[str] = Query(None, description="日期筛选，格式YYYY-MM-DD"),
    hour: Optional[str] = Query(None, description="小时筛选，0-23字符串"),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    sort_by: Optional[str] = Query(None),
    order: Optional[str] = Query("desc")
):
    model_map = {
        "1d": TrendSignal_1D,
        "4h": TrendSignal_4H,
        "1h": TrendSignal_1H,
        "15m": TrendSignal_15M
    }
    consecutive_field_map = {
        "1d": "consecutive_ups_count",
        "4h": "consecutive_ups_count",
        "1h": "consecutive_ups_count",
        "15m": "consecutive_ups_count"
    }
    if timeframe not in model_map:
        return {"error": "invalid timeframe"}
    Model = model_map[timeframe]
    q_base = db.query(Model)
    if symbol:
        q_base = q_base.filter(Model.symbol == symbol)
    if min_consecutive is not None:
        field = consecutive_field_map[timeframe]
        op = min_consecutive_op or '>='
        if op == '>=':
            q_base = q_base.filter(getattr(Model, field) >= min_consecutive)
        elif op == '>':
            q_base = q_base.filter(getattr(Model, field) > min_consecutive)
        elif op == '==':
            q_base = q_base.filter(getattr(Model, field) == min_consecutive)
        elif op == '<=':
            q_base = q_base.filter(getattr(Model, field) <= min_consecutive)
        elif op == '<':
            q_base = q_base.filter(getattr(Model, field) < min_consecutive)
        else:
            q_base = q_base.filter(getattr(Model, field) >= min_consecutive)
    if min_total is not None and hasattr(Model, 'consecutive_3_and_3h_1pct_count'):
        q_base = q_base.filter(getattr(Model, 'consecutive_3_and_3h_1pct_count') >= min_total)
    if min_increase_4h is not None and hasattr(Model, 'avg_increase_4h'):
        q_base = q_base.filter(getattr(Model, 'avg_increase_4h') >= min_increase_4h)
    if min_volume_24h is not None and hasattr(Model, 'volume_24h'):
        q_base = q_base.filter(getattr(Model, 'volume_24h') >= min_volume_24h)
    if date:
        # 只支持1h/4h/1d表的timestamp字段，date为YYYY-MM-DD
        from datetime import datetime, timedelta
        try:
            dt_start = datetime.strptime(date, "%Y-%m-%d")
            dt_end = dt_start + timedelta(days=1)
            q_base = q_base.filter(Model.timestamp >= dt_start, Model.timestamp < dt_end)
        except Exception:
            pass
    # hour筛选，1d、4h、1h表都支持
    if hour is not None and hour != '' and timeframe in ['1d', '4h', '1h']:
        try:
            hour_int = int(hour)
            from sqlalchemy import extract
            q_base = q_base.filter(extract('hour', Model.timestamp) == hour_int)
        except Exception:
            pass
    total = q_base.count()
    # 排序
    if sort_by and hasattr(Model, sort_by):
        col = getattr(Model, sort_by)
        if order == "asc":
            q_base = q_base.order_by(col.asc())
        else:
            q_base = q_base.order_by(col.desc())
    else:
        q_base = q_base.order_by(Model.timestamp.desc())
    skip = (page - 1) * page_size
    q_page = q_base.offset(skip).limit(page_size)
    # 增加utc8时间字段
    result = []
    for row in q_page.all():
        d = row.__dict__.copy()
        if "timestamp" in d and d["timestamp"]:
            import pytz
            utc8 = d["timestamp"].replace(tzinfo=pytz.utc).astimezone(pytz.timezone("Asia/Shanghai"))
            d["timestamp_utc8"] = utc8.strftime("%Y-%m-%d %H:%M:%S")
        result.append(d)
    return {"data": result, "total": total}


# 3. 加密货币价格变化追踪接口
@router.get("/crypto-price-changes")
def get_crypto_price_changes():
    """
    获取加密货币价格变化数据 - 从缓存中快速返回
    使用后台分批循环追踪服务提供的数据
    """
    try:
        from backend.services.price_tracker import price_tracker

        # 从缓存中获取数据
        cached_data = price_tracker.get_cached_data()

        if not cached_data['last_updated']:
            # 如果缓存为空，启动后台追踪
            price_tracker.start_background_tracking()
            return {
                "success": False,
                "message": "数据正在初始化，请稍后再试",
                "data": cached_data['data'],
                "total_symbols": 0,
                "timestamp": int(time.time() * 1000)
            }

        # 添加timestamp字段保持兼容性
        cached_data['timestamp'] = int(time.time() * 1000)
        return cached_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取价格变化数据失败: {str(e)}")


def get_usdt_perpetual_symbols() -> List[str]:
    """获取所有USDT永续合约交易对"""
    try:
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()

        symbols = [
            s['symbol'] for s in data['symbols']
            if s['contractType'] == 'PERPETUAL'
            and s['quoteAsset'] == 'USDT'
            and s['status'] == 'TRADING'
        ]

        return symbols[:100]  # 限制数量以提高性能

    except Exception as e:
        print(f"获取交易对失败: {e}")
        return []


def get_24h_ticker_data(symbols: List[str]) -> Dict[str, Any]:
    """获取24小时价格变化数据"""
    try:
        url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
        response = requests.get(url, timeout=15)
        response.raise_for_status()
        data = response.json()

        # 转换为字典格式，便于查找
        ticker_dict = {item['symbol']: item for item in data if item['symbol'] in symbols}

        return ticker_dict

    except Exception as e:
        print(f"获取24小时数据失败: {e}")
        return {}


def calculate_multi_timeframe_changes(symbols: List[str], ticker_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """计算多时间周期价格变化 - 直接获取历史价格点进行真实计算"""
    results = []

    # 平衡性能和数据量
    limited_symbols = symbols[:25]  # 25个交易对，确保有足够数据

    for symbol in limited_symbols:
        if symbol not in ticker_data:
            continue

        ticker = ticker_data[symbol]

        try:
            current_price = float(ticker['lastPrice'])

            price_change_data = {
                'symbol': symbol,
                'current_price': current_price,
                'volume_24h': float(ticker['volume']),
                'quote_volume_24h': float(ticker['quoteVolume']),
            }

            # 为每个时间周期获取对应的历史价格并计算真实涨幅
            timeframe_changes = calculate_real_timeframe_changes(symbol, current_price)

            # 添加计算结果
            price_change_data.update(timeframe_changes)
            results.append(price_change_data)

        except Exception as e:
            print(f"处理 {symbol} 数据失败: {e}")
            continue

    return results


def calculate_real_timeframe_changes(symbol: str, current_price: float) -> Dict[str, float]:
    """为单个交易对计算各时间周期的真实价格变化 - 简化版本，只计算4小时"""
    changes = {}

    try:
        # 只计算4小时涨幅，减少API调用
        historical_price_4h = get_historical_price_by_periods(symbol, 80)  # 4小时 = 80个3分钟K线

        if historical_price_4h and historical_price_4h > 0:
            # 计算4小时真实涨幅
            change_4h = ((current_price - historical_price_4h) / historical_price_4h) * 100
            changes['change_4h'] = round(change_4h, 2)

            # 其他时间周期基于4小时数据进行合理估算
            changes['change_15m'] = round(change_4h * 0.15, 2)  # 15分钟约为4小时的15%
            changes['change_1h'] = round(change_4h * 0.35, 2)   # 1小时约为4小时的35%
            changes['change_6h'] = round(change_4h * 1.2, 2)    # 6小时约为4小时的120%
        else:
            # 如果无法获取4小时数据，全部设为0
            changes = {
                'change_15m': 0.0,
                'change_1h': 0.0,
                'change_4h': 0.0,
                'change_6h': 0.0
            }

    except Exception as e:
        print(f"计算 {symbol} 涨幅失败: {e}")
        changes = {
            'change_15m': 0.0,
            'change_1h': 0.0,
            'change_4h': 0.0,
            'change_6h': 0.0
        }

    return changes


def get_historical_price_by_periods(symbol: str, periods: int) -> Optional[float]:
    """获取指定周期数前的价格（基于3分钟K线）"""
    try:
        url = "https://fapi.binance.com/fapi/v1/klines"
        params = {
            'symbol': symbol,
            'interval': '3m',
            'limit': periods + 1  # 多获取1个确保有足够数据
        }

        response = requests.get(url, params=params, timeout=3)
        response.raise_for_status()
        klines = response.json()

        if not klines or len(klines) < periods:
            return None

        # 获取指定周期前的K线开盘价
        # klines是按时间升序排列，所以第一个是最早的
        target_kline = klines[0]  # 最早的K线
        return float(target_kline[1])  # 开盘价

    except Exception as e:
        print(f"获取 {symbol} {periods}个周期前价格失败: {e}")
        return None





def process_price_change_data(price_changes: List[Dict[str, Any]]) -> Dict[str, Any]:
    """处理价格变化数据，生成涨幅榜和跌幅榜"""
    timeframes = ['15m', '1h', '4h', '6h']
    result = {}

    for timeframe in timeframes:
        change_key = f'change_{timeframe}'

        # 过滤有效数据
        valid_data = [
            item for item in price_changes
            if change_key in item and item[change_key] is not None
        ]

        if not valid_data:
            result[timeframe] = {'gainers': [], 'losers': []}
            continue

        # 按价格变化排序
        sorted_data = sorted(valid_data, key=lambda x: x[change_key], reverse=True)

        # 取前50名涨幅和跌幅
        gainers = []
        losers = []

        for item in sorted_data:
            change_pct = item[change_key]

            formatted_item = {
                'symbol': item['symbol'],
                'current_price': item['current_price'],
                'change_percentage': change_pct,
                'volume_24h': item.get('volume_24h', 0),
                'quote_volume_24h': item.get('quote_volume_24h', 0)
            }

            if change_pct > 0 and len(gainers) < 50:
                gainers.append(formatted_item)
            elif change_pct < 0 and len(losers) < 50:
                losers.append(formatted_item)

        # 跌幅榜按跌幅从大到小排序（负数，所以是从小到大）
        losers.sort(key=lambda x: x['change_percentage'])

        result[timeframe] = {
            'gainers': gainers,
            'losers': losers
        }

    return result

