from pydantic_settings import BaseSettings
from typing import Optional

class AuthSettings(BaseSettings):
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"  # 在生产环境中应该使用安全的密钥
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 管理员账号配置
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin"

    class Config:
        env_file = ".env"

settings = AuthSettings()