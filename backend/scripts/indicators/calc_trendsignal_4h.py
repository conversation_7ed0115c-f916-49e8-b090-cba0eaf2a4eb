import logging
from datetime import datetime
from sqlalchemy import and_
import pandas as pd
from backend.database.connection import get_session
from backend.database.models import RawKline_4H, TrendSignal_4H

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_symbols(session):
    symbols = session.query(RawKline_4H.symbol).distinct().all()
    return [s[0] for s in symbols]

def get_trendsignal_time_range(session, symbol):
    q = session.query(TrendSignal_4H).filter(TrendSignal_4H.symbol == symbol)
    min_row = q.order_by(TrendSignal_4H.timestamp.asc()).first()
    max_row = q.order_by(TrendSignal_4H.timestamp.desc()).first()
    if not min_row or not max_row:
        return None, None
    return min_row.timestamp, max_row.timestamp

def fetch_rawkline_for_symbol(session, symbol, start_time=None):  # end_time参数已移除，因ccxt不支持
    q = session.query(RawKline_4H).filter(RawKline_4H.symbol == symbol)
    if start_time:
        q = q.filter(RawKline_4H.timestamp > start_time)
    q = q.order_by(RawKline_4H.timestamp.asc())
    df = pd.read_sql(q.statement, session.bind)
    return df


def calc_single_symbol_indicators_4h(df):
    """计算单个symbol自身的技术指标（4h）"""
    df = df.sort_values('timestamp')        
    df['amplitude_4h'] = ((df['high'] - df['low']) / df['low']) * 100
    df['drawdown_4h'] = ((df['high'] - df['close']) / df['high']) * 100
    df['increase_4h'] = df['close'].pct_change(1) * 100
    df['increase_12h'] = df['increase_4h'].rolling(3).sum()
    df['increase_24h'] = df['increase_4h'].rolling(6).sum()
    df['increase_48h'] = df['increase_4h'].rolling(12).sum()
    df['k1_increase'] = df['increase_4h']
    df['k2_increase'] = df['increase_4h'].shift(1)
    df['k3_increase'] = df['increase_4h'].shift(2)
    df['volume_24h'] = df['volume'].rolling(6).sum()
    df['close_low_ratio'] = df['close'] / df['low']
    # 连续上涨K线数量
    def calc_streak(series):
        streak = []
        last = 0
        for t in ((series > 0).astype(int) - (series < 0).astype(int)):
            if t > 0:
                last = last + 1 if last > 0 else 1
            elif t < 0:
                last = last - 1 if last < 0 else -1
            else:
                last = 0
            streak.append(last)
        return streak
    df['consecutive_ups_count'] = calc_streak(df['increase_4h'])
    return df

def calc_market_indicators_4h(all_data):
    """全市场合并后，统一计算市场级指标（4h）"""

    def extract_base_symbol(symbol):
        for suffix in ['USDT', 'BUSD', 'USDC', 'TUSD', 'FDUSD', 'DAI']:
            if symbol.upper().endswith(suffix):
                return symbol.upper().replace(suffix, '')
        return symbol.upper()

    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('4H')
    # 市场排名
    all_data['rank_4h_market'] = all_data.groupby('timestamp')['increase_4h'].rank(ascending=False)
    all_data['rank_12h_market'] = all_data.groupby('timestamp')['increase_12h'].rank(ascending=False)
    all_data['rank_24h_market'] = all_data.groupby('timestamp')['increase_24h'].rank(ascending=False)
    all_data['rank_48h_market'] = all_data.groupby('timestamp')['increase_48h'].rank(ascending=False)
    # 连续3阳且12h涨幅>1%的累计（每个symbol累计次数）
    cond = (all_data['consecutive_ups_count'] == 3) & (all_data['increase_12h'] >= 1.0)
    all_data['consecutive_3_and_12h_1pct'] = cond.astype(int)
    all_data['consecutive_3_and_12h_1pct_count'] = cond.groupby(all_data['symbol']).cumsum()
    # 12h连续3且涨幅≥1%排名
    all_data['rank_in_12h_1pct'] = None
    mask = cond
    if mask.any():
        for ts, subdf in all_data[mask].groupby('timestamp'):
            ranks = subdf['increase_12h'].rank(ascending=False, method='min')
            all_data.loc[subdf.index, 'rank_in_12h_1pct'] = ranks.astype('Int64')
    # 总连续下跌段数：每个timestamp下有多少symbol处于连续下跌段（横截面统计）
    all_data['total_consecutive_downs'] = all_data.groupby('timestamp')['consecutive_ups_count'].transform(lambda x: (x <= -3).sum())
    # 市场均值
    avg_exclude_extreme = lambda x: x.sort_values(ascending=False).iloc[4:50].mean() if len(x) >= 50 else 0
    avg_decrease_only = lambda x: x[x < 0].mean() if (x < 0).any() else 0
    all_data['avg_increase_4h'] = all_data.groupby('timestamp')['increase_4h'].transform(avg_exclude_extreme)
    all_data['avg_increase_12h'] = all_data.groupby('timestamp')['increase_12h'].transform(avg_exclude_extreme)
    all_data['avg_decrease_4h'] = all_data.groupby('timestamp')['increase_4h'].transform(avg_decrease_only)
    # 市场上涨占比（up_pairs_ratio）
    total_pairs = all_data['symbol'].nunique()
    all_data['is_up'] = (all_data['close'] > all_data['open']).astype(int)
    up_pairs = all_data.groupby('timestamp')['is_up'].sum()
    all_data['up_pairs_ratio'] = all_data['timestamp'].map(lambda d: up_pairs.get(d, 0) / total_pairs)

    all_data['rank_4h_market'] = all_data.groupby('timestamp')['increase_4h'].rank(ascending=False, method='min')
    all_data['rank_12h_market'] = all_data.groupby('timestamp')['increase_12h'].rank(ascending=False, method='min')
    all_data['rank_24h_market'] = all_data.groupby('timestamp')['increase_24h'].rank(ascending=False, method='min')
    all_data['rank_48h_market'] = all_data.groupby('timestamp')['increase_48h'].rank(ascending=False, method='min')

    total_pairs = all_data['symbol'].nunique()
    up_pairs = all_data.groupby('timestamp').apply(lambda x: (x['close'] > x['open']).sum())
    all_data['up_pairs_ratio'] = all_data['timestamp'].map(lambda d: up_pairs.get(d, 0) / total_pairs)

    cond = (all_data['consecutive_ups_count'] == 3) & (all_data['increase_12h'] >= 1)
    all_data['consecutive_3_and_12h_1pct_count'] = 0
    for symbol, group in all_data.groupby('symbol'):
        mask = cond & (all_data['symbol'] == symbol)
        all_data.loc[mask, 'consecutive_3_and_12h_1pct_count'] = mask.groupby(all_data['symbol']).cumsum()[mask]

    # 处理4h、12h、24h、48h市场排名
    for rank_field, value_field in [
        ('rank_4h_market', 'increase_4h'),
        ('rank_12h_market', 'increase_12h'),
        ('rank_24h_market', 'increase_24h'),
        ('rank_48h_market', 'increase_48h')
    ]:
        all_data[rank_field] = None
        for ts, subdf in all_data.groupby('timestamp'):
            ranks = subdf[value_field].rank(ascending=False, method='min')
            all_data.loc[subdf.index, rank_field] = ranks

    # 12h连续3且涨幅≥1%排名
    all_data['rank_in_12h_1pct'] = None
    mask = cond
    if mask.any():
        for ts, subdf in all_data[mask].groupby('timestamp'):
            ranks = subdf['increase_12h'].rank(ascending=False, method='min')
            all_data.loc[subdf.index, 'rank_in_12h_1pct'] = ranks
    return all_data

# 字段元数据统一配置
FIELD_CONFIG = {
    'timestamp':   {'type': 'datetime', 'market': False},
    'consecutive_3_and_12h_1pct_count': {'type': 'int', 'market': True},
    'rank_in_12h_1pct': {'type': 'int', 'market': True},
    'rank_4h_market': {'type': 'int', 'market': True},
    'rank_12h_market': {'type': 'int', 'market': True},
    'rank_24h_market': {'type': 'int', 'market': True},
    'rank_48h_market': {'type': 'int', 'market': True},
    'avg_increase_4h': {'type': 'float', 'market': True},
    'avg_increase_12h': {'type': 'float', 'market': True},
    'amplitude_4h': {'type': 'float', 'market': False},
    'drawdown_4h': {'type': 'float', 'market': False},
    'increase_4h': {'type': 'float', 'market': False},
    'increase_12h': {'type': 'float', 'market': False},
    'increase_24h': {'type': 'float', 'market': False},
    'increase_48h': {'type': 'float', 'market': False},
    'k1_increase': {'type': 'float', 'market': False},
    'k2_increase': {'type': 'float', 'market': False},
    'k3_increase': {'type': 'float', 'market': False},
    'volume_24h': {'type': 'float', 'market': False},
    'close_low_ratio': {'type': 'float', 'market': False},
    'up_pairs_ratio': {'type': 'float', 'market': True},
    'consecutive_ups_count': {'type': 'int', 'market': False},
    'total_consecutive_downs': {'type': 'int', 'market': True},
}

def dataframe_to_trendsignal_records(df, symbol, market_snap=None):
    cols = list(FIELD_CONFIG.keys())
    market_fields = [k for k, v in FIELD_CONFIG.items() if v['market']]
    int_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'int']
    float_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'float']

    import traceback
    logger.info(f"[DEBUG] DataFrame columns: {list(df.columns)}")
    records = []
    # 保留所有行，不做consecutive_ups_count过滤
    if market_snap is not None:
        # 只用market_snap覆盖非排名类字段，排名字段保留自身真实值
        rank_fields = ['rank_4h_market', 'rank_12h_market', 'rank_24h_market', 'rank_48h_market', 'rank_in_12h_1pct']
        for field in market_fields:
            if field not in rank_fields:
                df[field] = df['timestamp'].map(lambda ts: market_snap.get(ts, {}).get(field))
    import pandas as pd
    for field in int_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0)
                df[field] = df[field].clip(lower=-9223372036854775808, upper=9223372036854775807).astype(int)
            
                continue
    for field in float_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0).astype(float)
            
                continue
    for idx, row in df.iterrows():
        try:
            record = TrendSignal_4H(
                **{k: row.get(k) for k in cols},
                symbol=symbol,
            )
            records.append(record)
        except Exception as e:
            logger.error(f"[ERROR] Failed to build record at idx={idx}: {e}\n{traceback.format_exc()}")
    return records

def main():
    session = get_session()
    symbols = get_symbols(session)
    logger.info(f"共{len(symbols)}个symbol待计算")
    symbol_dfs = []
        
    def extract_base_symbol(symbol):
        for suffix in ['USDT', 'BUSD', 'USDC', 'TUSD', 'FDUSD', 'DAI']:
            if symbol.upper().endswith(suffix):
                return symbol.upper().replace(suffix, '')
        return symbol.upper()
    for symbol in symbols:
        min_ts, max_ts = get_trendsignal_time_range(session, symbol)
        df = None
        if min_ts is None:
            logger.info(f"trendsignal表无数据，计算全量: {symbol}")
            df = fetch_rawkline_for_symbol(session, symbol)
        else:
            logger.info(f"trendsignal已存在数据，增量计算 {max_ts} 之后的数据: {symbol}")
            df = fetch_rawkline_for_symbol(session, symbol, start_time=max_ts)
        if df is None or df.empty:
            logger.info(f"{symbol} 无需计算")
            continue
        base_symbol = extract_base_symbol(symbol)
        
        df = calc_single_symbol_indicators_4h(df)
        df['symbol'] = symbol
        symbol_dfs.append(df)
    if not symbol_dfs:
        logger.info("无可处理数据")
        session.close()
        return
    all_data = pd.concat(symbol_dfs, ignore_index=True)
    all_data = calc_market_indicators_4h(all_data)
    for symbol in symbols:
        df = all_data[all_data['symbol'] == symbol].copy()
        # 只保留连续上涨>=2、连续下跌<=-2、累计>0或排名有值的行
        df = df[(df['consecutive_ups_count'] >= 2) |
                (df['consecutive_ups_count'] <= -2) |
                (df['consecutive_3_and_12h_1pct_count'] > 0) |
                (df['rank_in_12h_1pct'].notnull())].copy()
        if df.empty:
            continue
        records = dataframe_to_trendsignal_records(df, symbol)
        timestamps = [r.timestamp for r in records]
        existing = set(
            t[0] for t in session.query(TrendSignal_4H.timestamp)
            .filter(TrendSignal_4H.symbol == symbol)
            .filter(TrendSignal_4H.timestamp.in_(timestamps)).all()
        )
        new_records = [r for r in records if r.timestamp not in existing]
        if not new_records:
            logger.info(f"{symbol} 本次无新增数据")
            continue
        session.bulk_save_objects(new_records, return_defaults=False)
        session.commit()
        logger.info(f"{symbol} 写入 {len(new_records)} 条趋势信号数据")
    session.close()
    logger.info("全部计算完成")

if __name__ == '__main__':
    main()
