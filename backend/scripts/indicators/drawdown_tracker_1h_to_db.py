# coding: utf-8
"""
批量写入 drawdown_tracker_1h 表
- 计算所有symbol的跌幅追踪数据
- upsert到 drawdown_tracker_1h
"""
import pandas as pd
from sqlalchemy import desc
from backend.database.connection import get_session
from backend.database.models import RawKline_1H, OpenInterest, DrawdownTracker1H
from datetime import datetime, timezone

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fetch_and_store_binance_futures_pair_info():
    """
    下载币安USDT永续合约所有交易对的基础信息并写入BinanceFuturesPairInfo表
    """
    import requests
    from backend.database.models import BinanceFuturesPairInfo
    session = get_session()
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    resp = requests.get(url, timeout=10)
    resp.raise_for_status()
    data = resp.json()
    count = 0
    for s in data['symbols']:
        if s['contractType'] == 'PERPETUAL' and s['quoteAsset'] == 'USDT':
            symbol = s['symbol']
            base_asset = s['baseAsset']
            quote_asset = s['quoteAsset']
            onboard_date = s.get('onboardDate')
            pair_type = s.get('contractType')
            status = s.get('status')
            if onboard_date is None:
                onboard_date = 0
            row = session.query(BinanceFuturesPairInfo).filter_by(symbol=symbol).first()
            if row:
                row.base_asset = base_asset
                row.quote_asset = quote_asset
                row.onboard_date = onboard_date
                row.pair_type = pair_type
                row.status = status
            else:
                row = BinanceFuturesPairInfo(
                    symbol=symbol,
                    base_asset=base_asset,
                    quote_asset=quote_asset,
                    onboard_date=onboard_date,
                    pair_type=pair_type,
                    status=status
                )
                session.add(row)
            count += 1
    session.commit()
    session.close()
    logger.info(f"已写入/更新 {count} 个Binance USDT永续合约交易对到BinanceFuturesPairInfo表")

# 原有函数不变
def get_drawdown_info(symbol: str, session):
    # 获取上线日期
    onboard_date = None
    from backend.database.models import BinanceFuturesPairInfo
    pair = session.query(BinanceFuturesPairInfo).filter_by(symbol=symbol).first()
    onboard_date = pair.onboard_date if pair and pair.onboard_date else None
    latest_kline = session.query(RawKline_1H).filter_by(symbol=symbol).order_by(desc(RawKline_1H.timestamp)).limit(1).first()
    if not latest_kline:
        return None
    price = latest_kline.close
    klines = session.query(RawKline_1H).filter_by(symbol=symbol).order_by(desc(RawKline_1H.timestamp)).limit(4200).all()
    if not klines or len(klines) < 10:
        return None
    closes = [k.close for k in klines]
    high_365d = max(closes[:8760]) if len(closes) >= 8760 else max(closes)
    high_700d = max(closes)
    drawdown_365d = (price - high_365d) / high_365d * 100 if high_365d else None
    drawdown_700d = (price - high_700d) / high_700d * 100 if high_700d else None
    volume_24h = sum([k.volume for k in klines[:24]]) if len(klines) >= 24 else sum([k.volume for k in klines])

    return {
        'symbol': symbol,
        'price': price,
        'high_365d': high_365d,
        'drawdown_365d': drawdown_365d,
        'high_700d': high_700d,
        'drawdown_700d': drawdown_700d,
        'volume_24h': volume_24h,
        
        
        'onboard_date': onboard_date,
        'updated_at': datetime.now(timezone.utc)
    }

def main():
    fetch_and_store_binance_futures_pair_info()  # 先同步币安合约基础信息
    session = get_session()
    symbols = session.query(RawKline_1H.symbol).distinct().all()
    for symbol_tuple in symbols:
        symbol = symbol_tuple[0]
        info = get_drawdown_info(symbol, session)
        if info is None:
            continue
        # upsert
        row = session.query(DrawdownTracker1H).filter_by(symbol=symbol).first()
        if row:
            for k, v in info.items():
                setattr(row, k, v)
        else:
            row = DrawdownTracker1H(**info)
            session.add(row)
        session.commit()
    session.close()

if __name__ == '__main__':
    main()
