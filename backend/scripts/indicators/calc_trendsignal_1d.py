import logging
from datetime import datetime
from sqlalchemy import and_
import pandas as pd
from backend.database.connection import get_session
from backend.database.models import RawKline_1D, TrendSignal_1D

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_symbols(session):
    symbols = session.query(RawKline_1D.symbol).distinct().all()
    return [s[0] for s in symbols]

def get_trendsignal_time_range(session, symbol):
    q = session.query(TrendSignal_1D).filter(TrendSignal_1D.symbol == symbol)
    min_row = q.order_by(TrendSignal_1D.timestamp.asc()).first()
    max_row = q.order_by(TrendSignal_1D.timestamp.desc()).first()
    if not min_row or not max_row:
        return None, None
    return min_row.timestamp, max_row.timestamp

def fetch_rawkline_for_symbol(session, symbol, start_time=None):  # end_time参数已移除，因ccxt不支持
    q = session.query(RawKline_1D).filter(RawKline_1D.symbol == symbol)
    if start_time:
        q = q.filter(RawKline_1D.timestamp > start_time)
    q = q.order_by(RawKline_1D.timestamp.asc())
    df = pd.read_sql(q.statement, session.bind)
    return df


def calc_single_symbol_indicators_1d(df):
    """计算单个symbol自身的技术指标（1d）"""
    df = df.sort_values('timestamp')
    df['amplitude_1d'] = ((df['high'] - df['low']) / df['low']) * 100
    df['drawdown_1d'] = ((df['high'] - df['close']) / df['high']) * 100
    df['increase_1d'] = df['close'].pct_change(1) * 100
    df['increase_3d'] = df['increase_1d'].rolling(3).sum()
    df['increase_4d'] = df['increase_1d'].rolling(4).sum()
    df['increase_7d'] = df['increase_1d'].rolling(7).sum()
    df['increase_14d'] = df['increase_1d'].rolling(14).sum()
    df['k1_increase'] = df['increase_1d']
    df['k2_increase'] = df['increase_1d'].shift(1)
    df['k3_increase'] = df['increase_1d'].shift(2)
    df['volume_24h'] = df['volume']  # 1d周期直接用当前K线volume
    df['close_low_ratio'] = df['close'] / df['low']
    # 连续上涨K线数量
    def calc_streak(series):
        streak = []
        last = 0
        for t in ((series > 0).astype(int) - (series < 0).astype(int)):
            if t > 0:
                last = last + 1 if last > 0 else 1
            elif t < 0:
                last = last - 1 if last < 0 else -1
            else:
                last = 0
            streak.append(last)
        return streak
    df['consecutive_ups_count'] = calc_streak(df['increase_1d'])
    # 均值字段补充
    df['avg_increase_1d'] = df['increase_1d']
    df['avg_increase_4d'] = df['increase_1d'].rolling(4).mean()
    df['avg_increase_14d'] = df['increase_1d'].rolling(14).mean()
    return df

def calc_market_indicators_1d(all_data):
    """全市场合并后，统一计算市场级指标（1d）"""
    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('D')
    # 市场排名
    all_data['rank_1d_market'] = all_data.groupby('timestamp')['increase_1d'].rank(ascending=False, method='min')
    all_data['rank_3d_market'] = all_data.groupby('timestamp')['increase_3d'].rank(ascending=False, method='min')
    all_data['rank_4d_market'] = all_data.groupby('timestamp')['increase_4d'].rank(ascending=False, method='min')
    all_data['rank_7d_market'] = all_data.groupby('timestamp')['increase_7d'].rank(ascending=False, method='min')
    # 市场上涨占比（up_pairs_ratio）
    total_pairs = all_data['symbol'].nunique()
    all_data['is_up'] = (all_data['close'] > all_data['open']).astype(int)
    up_pairs = all_data.groupby('timestamp')['is_up'].sum()
    all_data['up_pairs_ratio'] = all_data['timestamp'].map(lambda d: up_pairs.get(d, 0) / total_pairs)

    # 连续3阳且3d涨幅≥1%累计（全市场累计数量，所有symbol该字段都一样）
    cond = (all_data['consecutive_ups_count'] == 3) & (all_data['increase_3d'] >= 1.0)
    all_data['consecutive_3_and_3d_1pct'] = cond.astype(int)
    # 统计每个timestamp全市场满足条件的symbol数量（累计）
    all_data['consecutive_3_and_3d_1pct_count'] = all_data.groupby('timestamp')['consecutive_3_and_3d_1pct'].transform('sum')

    # 3d连续3且涨幅≥1%排名（每个timestamp下，所有满足条件的symbol降序排名）
    all_data['rank_in_3d_1pct'] = 0
    mask = cond
    if mask.any():
        for ts, subdf in all_data[mask].groupby('timestamp'):
            ranks = subdf['increase_3d'].rank(ascending=False, method='min')
            all_data.loc[subdf.index, 'rank_in_3d_1pct'] = ranks.astype('Int64')
    # 连续下跌累计数（全市场累计）
    down_mask = all_data['consecutive_ups_count'] <= -2
    down_timestamps = sorted(set(all_data.loc[down_mask, 'timestamp'].tolist()))
    all_ts_sorted = sorted(all_data['timestamp'].unique())
    down_count_dict = {}
    count = 0
    for ts in all_ts_sorted:
        while count < len(down_timestamps) and down_timestamps[count] <= ts:
            count += 1
        down_count_dict[ts] = count
    all_data['total_consecutive_downs'] = all_data['timestamp'].map(down_count_dict)
    return all_data

# 字段元数据统一配置
FIELD_CONFIG = {
    'timestamp':   {'type': 'datetime', 'market': False},
    'total_consecutive_downs': {'type': 'int', 'market': True},
    'rank_in_3d_1pct': {'type': 'int', 'market': True},
    'rank_1d_market': {'type': 'int', 'market': True},
    'rank_3d_market': {'type': 'int', 'market': True},
    'rank_4d_market': {'type': 'int', 'market': True},
    'rank_7d_market': {'type': 'int', 'market': True},
    'avg_increase_1d': {'type': 'float', 'market': True},
    'avg_increase_4d': {'type': 'float', 'market': True},
    'avg_increase_14d': {'type': 'float', 'market': True},
    'amplitude_1d': {'type': 'float', 'market': False},
    'drawdown_1d': {'type': 'float', 'market': False},
    'increase_1d': {'type': 'float', 'market': False},
    'increase_3d': {'type': 'float', 'market': False},
    'increase_4d': {'type': 'float', 'market': False},
    'increase_7d': {'type': 'float', 'market': False},
    'increase_14d': {'type': 'float', 'market': False},
    'k1_increase': {'type': 'float', 'market': False},
    'k2_increase': {'type': 'float', 'market': False},
    'k3_increase': {'type': 'float', 'market': False},
    'volume_24h': {'type': 'float', 'market': False},
    'close_low_ratio': {'type': 'float', 'market': False},
    'up_pairs_ratio': {'type': 'float', 'market': True},
    'consecutive_ups_count': {'type': 'int', 'market': False},
    'consecutive_3_and_3d_1pct_count': {'type': 'float', 'market': True},
    'consecutive_downs': {'type': 'int', 'market': False},
    'total_consecutive_downs': {'type': 'int', 'market': True},
    
}

def dataframe_to_trendsignal_records(df, symbol, market_snap=None):
    import pandas as pd
    cols = list(FIELD_CONFIG.keys())
    market_fields = [k for k, v in FIELD_CONFIG.items() if v['market']]
    int_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'int']
    float_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'float']

    # 不做过滤，保留所有行
    df = df[[col for col in cols if col in df.columns]].copy()
    if market_snap is not None:
        # 只用market_snap覆盖非排名类字段，排名字段保留自身真实值
        rank_fields = ['rank_1d_market', 'rank_3d_market', 'rank_4d_market', 'rank_7d_market', 'rank_in_3d_1pct']
        for field in market_fields:
            if field not in rank_fields:
                df[field] = df['timestamp'].map(lambda ts: market_snap.get(ts, {}).get(field))
    for field in int_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0)
                df[field] = df[field].clip(lower=-9223372036854775808, upper=9223372036854775807).astype(int)
            
                continue
    for field in float_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0).astype(float)
            
                continue
    records = []
    for _, row in df.iterrows():
        record = TrendSignal_1D(
            **{k: row.get(k) for k in cols},
            symbol=symbol,
        )
        records.append(record)
    return records


def main():
    session = get_session()
    symbols = get_symbols(session)
    logger.info(f"共{len(symbols)}个symbol待计算")
    symbol_dfs = []
        
    def extract_base_symbol(symbol):
        # 假设所有主币都是前缀，去掉USDT、BUSD、USDC等常见计价币后缀
        for suffix in ['USDT', 'BUSD', 'USDC', 'TUSD', 'FDUSD', 'DAI']:
            if symbol.upper().endswith(suffix):
                return symbol.upper().replace(suffix, '')
        return symbol.upper()
    for symbol in symbols:
        try:
            logger.info(f"处理symbol: {symbol}")
            min_ts, max_ts = get_trendsignal_time_range(session, symbol)
            if min_ts is None:
                logger.info("trendsignal表无数据，计算全量")
                df = fetch_rawkline_for_symbol(session, symbol)
            
                logger.info(f"trendsignal已存在数据，增量计算 {max_ts} 之后的数据")
                df = fetch_rawkline_for_symbol(session, symbol, start_time=max_ts)
            if df.empty:
                logger.info(f"{symbol} 无需计算")
                continue
            base_symbol = extract_base_symbol(symbol)
            
            df = calc_single_symbol_indicators_1d(df)
            df['symbol'] = symbol
            symbol_dfs.append(df)
        except Exception as e:
            import traceback
            logger.error(f"处理 {symbol} 出错: {e}\n{traceback.format_exc()}")
            session.rollback()
    if not symbol_dfs:
        logger.info("无可处理数据")
        session.close()
        return
    all_data = pd.concat(symbol_dfs, ignore_index=True)
    all_data = calc_market_indicators_1d(all_data)
    for symbol in symbols:
        df = all_data[all_data['symbol'] == symbol].copy()
        # 只保留连续上涨>=2、连续下跌<=-2、累计>0或排名有值的行（与4h同步）
        df = df[(df['consecutive_ups_count'] >= 2) |
                (df['consecutive_ups_count'] <= -2) |
                (df.get('consecutive_3_and_3d_1pct_count', 0) > 0) |
                (df.get('rank_in_3d_1pct', None).notnull() if 'rank_in_3d_1pct' in df else False)].copy()
        if df.empty:
            continue
        records = dataframe_to_trendsignal_records(df, symbol)
        timestamps = [r.timestamp for r in records]
        existing = set(
            t[0] for t in session.query(TrendSignal_1D.timestamp)
            .filter(TrendSignal_1D.symbol == symbol)
            .filter(TrendSignal_1D.timestamp.in_(timestamps)).all()
        )
        new_records = [r for r in records if r.timestamp not in existing]
        if not new_records:
            logger.info(f"{symbol} 本次无新增数据")
            continue
        session.bulk_save_objects(new_records, return_defaults=False)
        session.commit()
        logger.info(f"{symbol} 写入 {len(new_records)} 条趋势信号数据")
    session.close()
    logger.info("全部计算完成")


if __name__ == '__main__':
    main()
