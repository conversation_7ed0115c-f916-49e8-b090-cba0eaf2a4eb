from datetime import datetime
from sqlalchemy import and_
import pandas as pd
from backend.database.connection import get_session
from backend.database.models import RawKline_1H, TrendSignal_1H

def get_symbols(session):
    # 获取所有 symbol
    symbols = session.query(RawKline_1H.symbol).distinct().all()
    return [s[0] for s in symbols]

def get_trendsignal_time_range(session, symbol):
    # 获取该symbol在trendsignal表的最早和最晚时间
    q = session.query(TrendSignal_1H).filter(TrendSignal_1H.symbol == symbol)
    min_row = q.order_by(TrendSignal_1H.timestamp.asc()).first()
    max_row = q.order_by(TrendSignal_1H.timestamp.desc()).first()
    if not min_row or not max_row:
        return None, None
    return min_row.timestamp, max_row.timestamp

def fetch_rawkline_for_symbol(session, symbol, start_time=None):  
    # 获取rawkline_1h数据，可以指定起始时间
    q = session.query(RawKline_1H).filter(RawKline_1H.symbol == symbol)
    if start_time:
        q = q.filter(RawKline_1H.timestamp > start_time)
    q = q.order_by(RawKline_1H.timestamp.asc())
    df = pd.read_sql(q.statement, session.bind)
    return df

def calc_single_symbol_indicators(df):
    """计算单个symbol自身的技术指标"""
    # 只分析已收盘K线（timestamp < 当前整点UTC-1小时）
    df['timestamp'] = pd.to_datetime(df['timestamp'], utc=True)
    now_utc = pd.Timestamp.utcnow().floor('h')
    cutoff = now_utc - pd.Timedelta(hours=1)
    df = df[df['timestamp'] < cutoff]
    df = df.sort_values('timestamp')
    df['amplitude_1h'] = ((df['high'] - df['low']) / df['low']) * 100
    df['drawdown_1h'] = ((df['high'] - df['close']) / df['high']) * 100
    df['increase_1h'] = df['close'].pct_change(1) * 100
    df['increase_3h'] = df['increase_1h'].rolling(3).sum()
    df['increase_4h'] = df['increase_1h'].rolling(4).sum()
    df['increase_6h'] = df['increase_1h'].rolling(6).sum()
    df['increase_12h'] = df['increase_1h'].rolling(12).sum()
    df['k1_increase'] = df['increase_1h']
    df['k2_increase'] = df['increase_1h'].shift(1)
    df['k3_increase'] = df['increase_1h'].shift(2)
    df['volume_24h'] = df['volume'].rolling(24).sum()
    df['close_low_ratio'] = df['close'] / df['low']
    
    # 连续上涨K线数量
    def calc_streak(series):
        streak = []
        last = 0
        for t in ((series > 0).astype(int) - (series < 0).astype(int)):
            if t > 0:
                last = last + 1 if last > 0 else 1
            elif t < 0:
                last = last - 1 if last < 0 else -1
            else:
                last = 0
            streak.append(last)
        return streak
    df['consecutive_ups_count'] = calc_streak(df['increase_1h'])
    return df

def calc_market_indicators(all_data):
    """全市场合并后，统一计算市场级指标（1h）"""
    all_data['timestamp'] = pd.to_datetime(all_data['timestamp']).dt.floor('h')
    # 市场排名
    all_data['rank_1h_market'] = all_data.groupby('timestamp')['increase_1h'].rank(ascending=False)
    all_data['rank_3h_market'] = all_data.groupby('timestamp')['increase_3h'].rank(ascending=False)
    all_data['rank_4h_market'] = all_data.groupby('timestamp')['increase_4h'].rank(ascending=False)
    all_data['rank_6h_market'] = all_data.groupby('timestamp')['increase_6h'].rank(ascending=False)
    # 连续3阳且3h涨幅>1%的累计（每个symbol累计次数）
    cond = (all_data['consecutive_ups_count'] == 3) & (all_data['increase_3h'] >= 1.0)
    all_data['consecutive_3_and_3h_1pct'] = cond.astype(int)
    all_data['consecutive_3_and_3h_1pct_count'] = cond.groupby(all_data['symbol']).cumsum()
    # 3h连续3且涨幅≥1%排名
    all_data['rank_in_3h_1pct'] = None
    mask = cond
    if mask.any():
        for ts, subdf in all_data[mask].groupby('timestamp'):
            ranks = subdf['increase_3h'].rank(ascending=False, method='min')
            all_data.loc[subdf.index, 'rank_in_3h_1pct'] = ranks.astype('Int64')
    # 统计到当前为止的总连续下跌段数（全市场累计）
    down_mask = all_data['consecutive_ups_count'] <= -3
    down_timestamps = sorted(set(all_data.loc[down_mask, 'timestamp'].tolist()))
    all_ts_sorted = sorted(all_data['timestamp'].unique())
    down_count_dict = {}
    count = 0
    for ts in all_ts_sorted:
        while count < len(down_timestamps) and down_timestamps[count] <= ts:
            count += 1
        down_count_dict[ts] = count
    all_data['total_consecutive_downs'] = all_data['timestamp'].map(down_count_dict)
    # 市场均值
    avg_exclude_extreme = lambda x: x.sort_values(ascending=False).iloc[4:50].mean() if len(x) >= 50 else 0
    avg_decrease_only = lambda x: x[x < 0].mean() if (x < 0).any() else 0
    all_data['avg_increase_1h'] = all_data.groupby('timestamp')['increase_1h'].transform(avg_exclude_extreme)
    all_data['avg_increase_4h'] = all_data.groupby('timestamp')['increase_4h'].transform(avg_exclude_extreme)
    all_data['avg_increase_12h'] = all_data.groupby('timestamp')['increase_12h'].transform(avg_exclude_extreme)
    all_data['avg_decrease_1h'] = all_data.groupby('timestamp')['increase_1h'].transform(avg_decrease_only)
    all_data['avg_decrease_4h'] = all_data.groupby('timestamp')['increase_4h'].transform(avg_decrease_only)
    # 市场上涨占比（up_pairs_ratio）
    total_pairs = all_data['symbol'].nunique()
    all_data['is_up'] = (all_data['close'] > all_data['open']).astype(int)
    up_pairs = all_data.groupby('timestamp')['is_up'].sum()
    all_data['up_pairs_ratio'] = all_data['timestamp'].map(lambda d: up_pairs.get(d, 0) / total_pairs)

    return all_data

# 字段元数据统一配置
FIELD_CONFIG = {
    'timestamp':   {'type': 'datetime', 'market': False},
    'consecutive_ups_count': {'type': 'int', 'market': False},
    'total_consecutive_downs': {'type': 'int', 'market': True},
    'consecutive_3_and_3h_1pct_count': {'type': 'int', 'market': True},
    'rank_in_3h_1pct': {'type': 'int', 'market': True},
    'rank_1h_market': {'type': 'int', 'market': True},
    'rank_in_3h_1pct': {'type': 'int', 'market': True},

    'rank_3h_market': {'type': 'int', 'market': True},
    'rank_4h_market': {'type': 'int', 'market': True},
    'rank_6h_market': {'type': 'int', 'market': True},
    'avg_increase_1h': {'type': 'float', 'market': True},
    'avg_increase_4h': {'type': 'float', 'market': True},
    'avg_increase_12h': {'type': 'float', 'market': True},
    'amplitude_1h': {'type': 'float', 'market': False},
    'drawdown_1h': {'type': 'float', 'market': False},
    'increase_1h': {'type': 'float', 'market': False},
    'increase_3h': {'type': 'float', 'market': False},
    'increase_4h': {'type': 'float', 'market': False},
    'increase_6h': {'type': 'float', 'market': False},
    'increase_12h': {'type': 'float', 'market': False},
    'k1_increase': {'type': 'float', 'market': False},
    'k2_increase': {'type': 'float', 'market': False},
    'k3_increase': {'type': 'float', 'market': False},
    'volume_24h': {'type': 'float', 'market': False},
    'close_low_ratio': {'type': 'float', 'market': False},
    'up_pairs_ratio': {'type': 'float', 'market': True},
}

def dataframe_to_trendsignal_records(df):
    # 自动生成字段列表
    cols = list(FIELD_CONFIG.keys())
    int_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'int']
    float_fields = [k for k, v in FIELD_CONFIG.items() if v['type'] == 'float']
    records = []
    import pandas as pd
    for field in int_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0)
                df[field] = df[field].clip(lower=-9223372036854775808, upper=9223372036854775807).astype(int)
                continue
    for field in float_fields:
        if field in df:
            if pd.api.types.is_numeric_dtype(df[field]):
                df[field] = df[field].fillna(0).astype(float)
                continue
    for _, row in df.iterrows():
        record = TrendSignal_1H(
            **{k: row.get(k) for k in cols},
            symbol=row.get('symbol'),
        )
        records.append(record)
    return records

def main():
    session = get_session()
    symbols = get_symbols(session)
    symbol_dfs = []
    symbol_min_ts = {}
    symbol_max_ts = {}
    for symbol in symbols:
        min_ts, max_ts = get_trendsignal_time_range(session, symbol)
        symbol_min_ts[symbol] = min_ts
        symbol_max_ts[symbol] = max_ts
        if min_ts is None:
            df = fetch_rawkline_for_symbol(session, symbol)
        else:
            df = fetch_rawkline_for_symbol(session, symbol, start_time=max_ts)
        if df.empty:
            continue
        df['symbol'] = symbol
        df = calc_single_symbol_indicators(df)
        symbol_dfs.append(df)
    if not symbol_dfs:
        session.close()
        return
    all_data = pd.concat(symbol_dfs, ignore_index=True)
    all_data = calc_market_indicators(all_data)
    for symbol, df in all_data.groupby('symbol'):
        min_ts = symbol_min_ts.get(symbol)
        if min_ts is not None:
            max_ts = symbol_max_ts[symbol]
            if max_ts is not None:
                max_ts = pd.Timestamp(max_ts)
                if max_ts.tzinfo is None:
                    max_ts = max_ts.tz_localize('UTC')
        records = dataframe_to_trendsignal_records(df)
        timestamps = [r.timestamp for r in records]
        existing = set(
            t[0] for t in session.query(TrendSignal_1H.timestamp)
            .filter(TrendSignal_1H.symbol == symbol)
            .filter(TrendSignal_1H.timestamp.in_(timestamps)).all()
        )
        new_records = [r for r in records if r.timestamp not in existing]
        if not new_records:
            continue
        session.bulk_save_objects(new_records, return_defaults=False)
        session.commit()
    session.close()

if __name__ == '__main__':
    main()
