import logging
import argparse
import requests
import pandas as pd
from datetime import datetime, timedelta, timezone
from backend.database.connection import get_session
from backend.services.kline_fetcher import K<PERSON>Fetcher
from concurrent.futures import ThreadPoolExecutor, as_completed
from backend.database.models import RawKline_1H, RawKline_4H, RawKline_1D, OpenInterest, FundingRate


logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

KLINE_MODEL_MAP = {
    '1h': RawKline_1H,
    '4h': RawKline_4H,
    '1d': RawKline_1D,
}

def get_usdt_perpetual_symbols():
    import time
    import requests
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    for attempt in range(2):  # 最多重试一次
        try:
            resp = requests.get(url, timeout=10)
            resp.raise_for_status()
            data = resp.json()
            symbols = [s['symbol'] for s in data['symbols'] if s['contractType'] == 'PERPETUAL' and s['quoteAsset'] == 'USDT' and s['status'] == 'TRADING']
            logger.info(f"自动获取到 {len(symbols)} 个USDT永续合约交易对")
            return symbols
        except requests.exceptions.HTTPError as e:
            if hasattr(e.response, 'status_code') and e.response.status_code in (418, 429):
                logger.warning("获取交易对时被限流，暂停5分钟后重试...")
                time.sleep(300)
            else:
                raise
    raise RuntimeError("连续两次获取币安交易对失败，脚本退出")

def parse_datetime(dt_str):
    if dt_str is None:
        return None
    try:
        return datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return datetime.strptime(dt_str, "%Y-%m-%d")

def fetch_and_store_kline(timeframe, start_time, end_time, symbols, max_workers=12):
    fetcher = KlineFetcher()
    Model = KLINE_MODEL_MAP[timeframe]
    total_inserted = 0
    def handle_symbol(symbol):
        session = get_session()
        inserted = 0
        import time
        import requests
        try:
            logger.info(f"下载 {symbol} 的{timeframe} K线数据（{start_time} ~ {end_time}）...")
            try:
                df = fetcher.fetch_klines(symbol, timeframe=timeframe, since=start_time, end=end_time)
            except requests.exceptions.HTTPError as e:
                if hasattr(e.response, 'status_code') and e.response.status_code in (418, 429):
                    logger.warning(f"{symbol} 被限流，暂停5分钟后重试...")
                    time.sleep(300)
                    df = fetcher.fetch_klines(symbol, timeframe=timeframe, since=start_time, end=end_time)
                else:
                    raise
            if df.empty:
                logger.info(f"{symbol} 无数据，跳过")
                return 0
            # 严格过滤未收盘K线，只保留close_time < 当前整点UTC-1小时
            now_utc = datetime.now(timezone.utc).replace(minute=0, second=0, microsecond=0)
            cutoff = now_utc - timedelta(hours=1)
            if 'close_time' in df.columns:
                df['close_time'] = pd.to_datetime(df['close_time'], unit='ms', utc=True)
                df = df[df['close_time'] < cutoff]
            else:
                raise ValueError("K线数据缺少close_time字段，无法过滤未收盘K线")
            records = []
            for _, row in df.iterrows():
                record = Model(
                    exchange='binance',
                    symbol=symbol,
                    base_asset=symbol.replace('USDT',''),
                    timestamp=row['datetime'],
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume']
                )
                records.append(record)
            if records:
                try:
                    from sqlalchemy.dialects.postgresql import insert
                    dict_records = [r.__dict__ for r in records]
                    for d in dict_records:
                        d.pop('_sa_instance_state', None)
                    stmt = insert(Model).values(dict_records)
                    stmt = stmt.on_conflict_do_nothing(index_elements=['exchange', 'symbol', 'timestamp'])
                    session.execute(stmt)
                    session.commit()
                    logger.info(f"{symbol} 批量插入 {len(records)} 条K线数据（重复自动跳过）")
                    inserted += len(records)
                except Exception as e:
                    logger.error(f"批量插入 {symbol} 时出错: {e}")
                    session.rollback()
        except Exception as e:
            logger.error(f"处理 {symbol} 时出错: {e}")
            session.rollback()
        finally:
            session.close()
        import time
        time.sleep(0.3)
        return inserted

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(handle_symbol, symbol): symbol for symbol in symbols}
        for future in as_completed(futures):
            total_inserted += future.result()
    logger.info(f"全部完成，共写入 {total_inserted} 条K线数据")

def fetch_and_store_open_interest(period, start_time, end_time, symbols, max_workers=12):
    fetcher = KlineFetcher()
    total_inserted = 0
    def handle_symbol(symbol):
        session = get_session()
        inserted = 0
        try:
            logger.info(f"下载 {symbol} 的{period}持仓量数据（{start_time} ~ {end_time}）...")
            df = fetcher.fetch_open_interest(symbol, period, start_time, end_time)
            if df.empty:
                logger.info(f"{symbol} 无数据，跳过")
                return 0
            records = []
            for _, row in df.iterrows():
                record = OpenInterest(
                    exchange='binance',
                    symbol=symbol,
                    base_asset=symbol.replace('USDT',''),
                    timestamp=row['timestamp'].to_pydatetime(),
                    timeframe=period,
                    open_interest=row['sumOpenInterest']
                )
                records.append(record)
            if records:
                timestamps = [r.timestamp for r in records]
                existing = set(
                    t[0] for t in session.query(OpenInterest.timestamp)
                    .filter(OpenInterest.exchange == 'binance')
                    .filter(OpenInterest.symbol == symbol)
                    .filter(OpenInterest.timeframe == period)
                    .filter(OpenInterest.timestamp.in_(timestamps))
                    .all()
                )
                new_records = [r for r in records if r.timestamp not in existing]
                if not new_records:
                    logger.info(f"{symbol} 本次无新增数据，全部已存在")
                else:
                    try:
                        session.bulk_save_objects(new_records, return_defaults=False)
                        session.commit()
                        logger.info(f"{symbol} 批量写入 {len(new_records)} 条持仓量数据")
                        inserted += len(new_records)
                    except Exception as e:
                        logger.error(f"批量写入 {symbol} 时出错: {e}")
                        session.rollback()
        except Exception as e:
            logger.error(f"处理 {symbol} 时出错: {e}")
            session.rollback()
        finally:
            session.close()
        import time
        time.sleep(0.3)
        return inserted

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(handle_symbol, symbol): symbol for symbol in symbols}
        for future in as_completed(futures):
            total_inserted += future.result()
    logger.info(f"全部完成，共写入 {total_inserted} 条持仓量数据")

def fetch_and_store_funding_rate(start_time, end_time, symbols, max_workers=12):
    fetcher = KlineFetcher()
    total_inserted = 0
    def handle_symbol(symbol):
        session = get_session()
        inserted = 0
        try:
            from sqlalchemy.dialects.postgresql import insert
            logger.info(f"下载 {symbol} 的资金费率数据（{start_time} ~ {end_time}）...")
            df = fetcher.fetch_funding_rate(symbol, start_time, end_time)
            if df.empty:
                logger.info(f"{symbol} 无数据，跳过")
                return 0
            # 数据标准化
            df['fundingTime'] = pd.to_datetime(df['fundingTime'], unit='ms', utc=True)
            records = []
            for _, row in df.iterrows():
                record = FundingRate(
                    exchange='binance',
                    symbol=symbol,
                    timestamp=row['fundingTime'],
                    funding_rate=row['fundingRate']
                )
                records.append(record)
            # 去重写入
            timestamps = [r.timestamp for r in records]
            existing = set(
                t[0] for t in session.query(FundingRate.timestamp)
                .filter(FundingRate.exchange == 'binance')
                .filter(FundingRate.symbol == symbol)
                .filter(FundingRate.timestamp.in_(timestamps))
                .all()
            )
            new_records = [r for r in records if r.timestamp not in existing]
            if not new_records:
                logger.info(f"{symbol} 本次无新增资金费率数据，全部已存在")
            else:
                try:
                    dict_records = [r.__dict__ for r in new_records]
                    for d in dict_records:
                        d.pop('_sa_instance_state', None)
                    stmt = insert(FundingRate).values(dict_records)
                    stmt = stmt.on_conflict_do_nothing(index_elements=['exchange', 'symbol', 'timestamp'])
                    session.execute(stmt)
                    session.commit()
                    logger.info(f"{symbol} 批量upsert写入 {len(new_records)} 条资金费率数据（重复自动跳过）")
                    inserted += len(new_records)
                except Exception as e:
                    logger.error(f"批量upsert写入 {symbol} 资金费率时出错: {e}")
                    session.rollback()
        except Exception as e:
            logger.error(f"处理 {symbol} 时出错: {e}")
            session.rollback()
        finally:
            session.close()
        import time
        time.sleep(0.3)
        return inserted

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(handle_symbol, symbol): symbol for symbol in symbols}
        for future in as_completed(futures):
            total_inserted += future.result()
    logger.info(f"全部完成，共写入 {total_inserted} 条资金费率数据")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--type', type=str, required=True, choices=['kline', 'open_interest', 'funding_rate', 'pair_info'], help='数据类型')
    parser.add_argument('--timeframe', type=str, default='1h', help='K线/持仓量周期，如1h,4h,1d')
    parser.add_argument('--start', type=str, default=None, help='起始日期，如2024-04-01 或 2024-04-01 00:00:00')
    parser.add_argument('--end', type=str, default=None, help='结束日期，如2024-04-20 或 2024-04-20 00:00:00')
    parser.add_argument('--symbols', type=str, default=None, help='逗号分隔的币对列表，如BTCUSDT,ETHUSDT，默认全部')
    args = parser.parse_args()

    symbols = args.symbols.split(',') if args.symbols else get_usdt_perpetual_symbols()
    start_time = parse_datetime(args.start) if args.start else None
    end_time = parse_datetime(args.end) if args.end else None

    if args.type == 'kline':
        if args.timeframe not in KLINE_MODEL_MAP:
            logger.error(f"不支持的K线周期: {args.timeframe}")
            return
        if not start_time or not end_time:
            # 增量刷新模式
            logger.info("未指定日期参数，自动进入增量刷新模式")
            session = get_session()
            now = datetime.utcnow()
            Model = KLINE_MODEL_MAP[args.timeframe]
            global_earliest = session.query(Model).order_by(Model.timestamp.asc()).first()
            global_earliest_time = global_earliest.timestamp if global_earliest else None
            for symbol in symbols:
                latest = session.query(Model).filter(Model.symbol == symbol).order_by(Model.timestamp.desc()).first()
                if latest:
                    s_time = latest.timestamp + (timedelta(hours=1) if args.timeframe == '1h' else timedelta(hours=4) if args.timeframe == '4h' else timedelta(days=1))
                    if s_time >= now:
                        logger.info(f"{symbol} 已是最新，无需拉取")
                        continue
                else:
                    s_time = global_earliest_time
                    if s_time is None:
                        logger.info(f"{symbol} 在数据库中无数据，且全表也无数据，跳过")
                        continue
                logger.info(f"{symbol} 增量拉取区间：{s_time} ~ {now}")
                fetch_and_store_kline(args.timeframe, s_time, now, [symbol])
            session.close()
        else:
            fetch_and_store_kline(args.timeframe, start_time, end_time, symbols)
    elif args.type == 'open_interest':
        if not start_time or not end_time:
            logger.info("未指定日期参数，自动进入增量刷新模式")
            session = get_session()
            now = datetime.utcnow()
            global_earliest = session.query(OpenInterest).filter(OpenInterest.timeframe == args.timeframe).order_by(OpenInterest.timestamp.asc()).first()
            global_earliest_time = global_earliest.timestamp if global_earliest else datetime(2020, 1, 1)
            for symbol in symbols:
                latest = session.query(OpenInterest).filter(OpenInterest.symbol == symbol, OpenInterest.timeframe == args.timeframe).order_by(OpenInterest.timestamp.desc()).first()
                if latest:
                    s_time = latest.timestamp + (timedelta(hours=1) if args.timeframe == '1h' else timedelta(hours=4) if args.timeframe == '4h' else timedelta(days=1))
                    if s_time >= now:
                        logger.info(f"{symbol} 已是最新，无需拉取")
                        continue
                else:
                    s_time = global_earliest_time
                    if s_time is None:
                        logger.info(f"{symbol} 在数据库中无数据，且全表也无数据，跳过")
                        continue
                logger.info(f"{symbol} 增量拉取区间：{s_time} ~ {now}")
                fetch_and_store_open_interest(args.timeframe, s_time, now, [symbol])
            session.close()
        else:
            fetch_and_store_open_interest(args.timeframe, start_time, end_time, symbols)
    elif args.type == 'funding_rate':
        if not start_time or not end_time:
            logger.info("未指定资金费率起止时间，自动进入增量刷新模式")
            from backend.database.models import FundingRate
            session = get_session()
            now = datetime.utcnow().replace(minute=0, second=0, microsecond=0)
            for symbol in symbols:
                latest = session.query(FundingRate).filter(FundingRate.symbol == symbol).order_by(FundingRate.timestamp.desc()).first()
                if latest:
                    s_time = latest.timestamp + timedelta(hours=1)
                    if s_time >= now:
                        logger.info(f"{symbol} 资金费率已是最新，无需拉取")
                        continue
                else:
                    logger.info(f"{symbol} 在数据库中无资金费率数据，跳过")
                    continue
                logger.info(f"{symbol} 资金费率增量拉取区间：{s_time} ~ {now}")
                fetch_and_store_funding_rate(s_time, now, [symbol])
            session.close()
        else:
            fetch_and_store_funding_rate(start_time, end_time, symbols)


if __name__ == '__main__':
    # fetch_and_store_binance_futures_pair_info()  # 这个函数在另一个文件中定义，暂时注释掉
    main() 