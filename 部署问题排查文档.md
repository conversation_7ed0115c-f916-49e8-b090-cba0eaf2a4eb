# K-line Signal 部署问题排查文档

## 问题描述
部署到服务器后，前端页面可以打开但无法登录，提示用户名或密码错误。

## 排查过程
1. 检查服务状态
   - nginx 服务正常运行
   - 前端服务（端口3000）正常运行
   - 后端服务（端口8000）启动失败

2. 发现问题
   - 后端日志显示 `AuthSettings` 对象缺少 `ADMIN_USERNAME` 属性
   - 检查发现 `auth_config.py` 中未定义管理员相关配置

## 解决方案
1. 修改配置文件
在 `backend/config/auth_config.py` 中添加管理员配置：
```python
class AuthSettings(BaseSettings):
    # 原有配置...
    
    # 添加管理员配置
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin"
```

2. 添加环境变量
在 `.env` 文件中添加管理员账号配置：
```bash
# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin
```

## 默认登录凭据
- 用户名：admin
- 密码：admin

## 部署注意事项
1. 确保 `.env` 文件包含所有必要的配置项
2. 确保配置类（如 `AuthSettings`）中定义了所有需要的字段
3. 环境变量的修改需要重启后端服务才能生效

## 重启服务命令
```bash
# 停止后端服务
pkill -f "python3 backend/main.py"

# 启动后端服务
cd /home/<USER>/k_line_signal
source venv/bin/activate
PYTHONPATH=/home/<USER>/k_line_signal nohup python3 backend/main.py > backend.log 2>&1 &
```
