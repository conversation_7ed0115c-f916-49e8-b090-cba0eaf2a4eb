import React from 'react'
import { Input } from '@/components/ui/input'
import { DatePicker } from '@/components/ui/date-picker'
import { Label } from '@/components/ui/label'
import { DateRangePicker } from '@/components/ui/date-range-picker'

interface FiltersProps {
  symbol: string
  onSymbolChange: (v: string) => void
  minDrawdown: string
  onMinDrawdownChange: (v: string) => void
  minVolume24h: string
  onMinVolume24hChange: (v: string) => void
  onboardDateRange: { from: Date | undefined, to: Date | undefined }
  onOnboardDateRangeChange: (range: import('react-day-picker').DateRange | undefined) => void
}

export function DrawdownTrackerFilters({
  symbol,
  onSymbolChange,
  minDrawdown,
  onMinDrawdownChange,
  minVolume24h,
  onMinVolume24hChange,
  date,
  onDateChange,
  onboardDateRange,
  onOnboardDateRangeChange,
}: FiltersProps) {
  return (
    <div className="flex flex-wrap gap-4 mt-6 mb-6 items-end">
      <div className="flex flex-col gap-1">
        <DateRangePicker value={onboardDateRange} onChange={onOnboardDateRangeChange} />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="symbol" value={symbol} onChange={e => onSymbolChange(e.target.value)} placeholder="交易对" className="w-36 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-drawdown" type="number" value={minDrawdown} onChange={e => onMinDrawdownChange(e.target.value)} placeholder="跌幅≥" className="w-28 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-volume24h" type="number" value={minVolume24h} onChange={e => onMinVolume24hChange(e.target.value)} placeholder="24H成交量≥" className="w-32 placeholder:text-muted-foreground" />
      </div>
    </div>
  )
}
