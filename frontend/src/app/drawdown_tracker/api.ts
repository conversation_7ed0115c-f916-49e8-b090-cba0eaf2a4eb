import { api } from '@/lib/api-client';

export interface DrawdownTracker {
  id: number;
  symbol: string;
  // 其他字段根据实际情况添加
}

export interface DrawdownTrackerApiResult {
  data: DrawdownTracker[];
  total: number;
}

export interface DrawdownTrackerFilters {
  skip?: number
  limit?: number
  date?: Date | undefined
  onboardDateFrom?: Date | undefined
  onboardDateTo?: Date | undefined
  symbol?: string
  minDrawdown?: string
  minVolume24h?: string
  order_by?: string
  desc?: boolean
  page?: number
  page_size?: number
}

export async function fetchDrawdownTracker(filters: DrawdownTrackerFilters = {}): Promise<DrawdownTrackerApiResult> {
  // 准备API参数
  const apiParams: Record<string, string | number | boolean | undefined> = {};

  if (filters.page) apiParams.page = filters.page;
  if (filters.page_size) apiParams.page_size = filters.page_size;
  if (filters.date) apiParams.date = filters.date.toISOString().slice(0, 10);
  if (filters.onboardDateFrom) apiParams.onboard_date_from = filters.onboardDateFrom.getTime();
  if (filters.onboardDateTo) apiParams.onboard_date_to = filters.onboardDateTo.getTime();
  if (filters.symbol) apiParams.symbol = filters.symbol;
  if (filters.minDrawdown) apiParams.min_drawdown = filters.minDrawdown;
  if (filters.minVolume24h) apiParams.min_volume_24h = filters.minVolume24h;
  if (typeof filters.skip === 'number') apiParams.skip = filters.skip;
  if (typeof filters.limit === 'number') apiParams.limit = filters.limit;
  if (filters.order_by) apiParams.order_by = filters.order_by;
  if (typeof filters.desc === 'boolean') apiParams.desc = filters.desc;

  // 使用API客户端发送请求
  try {
    const result = await api.get('/data/drawdown_tracker_1h', { params: apiParams });

    if (Array.isArray(result)) {
      return { data: result, total: result.length };
    }

    return { data: result.data, total: result.total };
  } catch (error) {
    console.error('获取跌幅数据失败:', error);
    throw new Error('获取跌幅数据失败');
  }
}
