import { ColumnDef } from "@tanstack/react-table"

export type DrawdownTracker = {
  symbol: string
  price: number
  high_365d: number
  drawdown_365d: number
  high_700d: number
  drawdown_700d: number
  volume_24h: number
  onboard_date?: number
  updated_at: string
}

export const columns: ColumnDef<DrawdownTracker>[] = [
  { accessorKey: "symbol", header: "币种" },
  {
    accessorKey: "onboard_date", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>上线日期
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => {
      const ts = getValue();
      if (!ts) return '-';
      const d = new Date(ts as number);
      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
    }
  },
  { accessorKey: "price", header: "现价", cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(4) : "-" },
  {
    accessorKey: "high_365d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>365天高点
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(4) : "-"
  },
  {
    accessorKey: "drawdown_365d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>365天跌幅%
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"
  },
  {
    accessorKey: "high_700d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>700天高点
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(4) : "-"
  },
  {
    accessorKey: "drawdown_700d", header: ({ column }) => (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>700天跌幅%
        <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
          {column.getIsSorted() === "asc" && "↑"}
          {column.getIsSorted() === "desc" && "↓"}
          {!column.getIsSorted() && <span style={{ fontSize: '13px', letterSpacing: '-2px', display: 'inline-block' }}>↑↓</span>}
        </span>
      </div>
    ),
    enableSorting: true,
    cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-"
  },
  {
    accessorKey: "volume_24h", header: "成交量(万)", enableSorting: true,
    cell: ({ getValue }) => {
      const v = getValue();
      if (v === undefined || v === null) return "-";
      return (Number(v) / 10_000).toFixed(2);
    }
  },
  { accessorKey: "updated_at", header: "更新时间", cell: ({ getValue }) => getValue() ? new Date(getValue() as string).toLocaleString() : "-" },
];
