"use client"
import { <PERSON>bar<PERSON>rovider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { AppSidebar } from '@/components/app-sidebar'
import { Separator } from '@/components/ui/separator'
import { DataTable } from '@/components/ui/data-table'
import { columns } from './columns'
import { DrawdownTrackerFilters } from './filters'
import { Alert } from '@/components/ui/alert'
import React, { useEffect, useState } from 'react'
import type { DrawdownTracker } from './columns'
import { fetchDrawdownTracker } from './api'

export default function DrawdownTrackerPage() {
  const [date, setDate] = useState<Date | undefined>(undefined)
  const [onboardDateRange, setOnboardDateRange] = useState<import('react-day-picker').DateRange | undefined>(undefined)
  const [symbol, setSymbol] = useState('')
  const [minDrawdown, setMinDrawdown] = useState('')
  const [minVolume24h, setMinVolume24h] = useState('')
  const [data, setData] = useState<DrawdownTracker[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // 新增排序状态
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const fetchPage = React.useCallback((page: number, pageSize: number, sortByParam?: string, sortOrderParam?: 'asc' | 'desc') => {
    setLoading(true)
    fetchDrawdownTracker({
      skip: page * pageSize,
      limit: pageSize,
      date,
      onboardDateFrom: onboardDateRange?.from,
      onboardDateTo: onboardDateRange?.to,
      symbol,
      minDrawdown,
      minVolume24h,
      order_by: sortByParam || sortBy,
      desc: (sortOrderParam || sortOrder) === 'desc',
    })
      .then(res => {
        setData(res.data)
        setTotal(res.total)
      })
      .catch(e => setError(e.message || '数据加载失败'))
      .finally(() => setLoading(false))
  }, [date, symbol, minDrawdown, minVolume24h, sortBy, sortOrder])

  useEffect(() => {
    fetchPage(pageIndex, pageSize)
  }, [pageIndex, pageSize, fetchPage, onboardDateRange])

  return (
    <SidebarProvider>
      <div className="flex h-screen w-screen">
        <AppSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <SidebarInset className="flex flex-col h-full">
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbPage>合约跌幅榜</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </header>
            <main className="flex-1 flex flex-col min-w-0">
              <div className="flex-1 flex flex-col min-w-0">
                {error && (
                  <div className="fixed left-1/2 bottom-12 z-50 -translate-x-1/2 w-full max-w-md animate-in fade-in-0 slide-in-from-bottom-10">
                    <Alert variant="destructive" title="获取跌幅数据失败">
                      {error}
                    </Alert>
                  </div>
                )}
                <div className="w-full flex-1 relative z-20 overflow-x-auto min-w-0">
                  <div className="px-4">
                    <DrawdownTrackerFilters
                      onboardDateRange={onboardDateRange || { from: undefined, to: undefined }}
                      onOnboardDateRangeChange={setOnboardDateRange}
                      symbol={symbol}
                      onSymbolChange={setSymbol}
                      minDrawdown={minDrawdown}
                      onMinDrawdownChange={setMinDrawdown}
                      minVolume24h={minVolume24h}
                      onMinVolume24hChange={setMinVolume24h}
                    />
                    <DataTable
                      columns={columns}
                      data={data}
                      pageIndex={pageIndex}
                      pageSize={pageSize}
                      total={total}
                      onPageChange={setPageIndex}
                      onPageSizeChange={setPageSize}
                      sortBy={sortBy}
                      sortOrder={sortOrder}
                      onSortChange={(field, order) => {
                        setSortBy(field);
                        setSortOrder(order);
                        setPageIndex(0);
                        fetchPage(0, pageSize, field, order);
                      }}
                    />
                  </div>
                </div>
                {loading && <div className="text-center p-4">加载中...</div>}
              </div>
            </main>
          </SidebarInset>
        </div>
      </div>
    </SidebarProvider>
  )
}
