import React from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'
import { DatePicker } from '@/components/ui/date-picker'
import { HourSelect4H } from '../trendsignal_1d/filters'

interface TrendSignal4HFiltersProps {
  symbol: string
  onSymbolChange: (v: string) => void
  minConsecutive: string
  onMinConsecutiveChange: (v: string) => void
  minTotal: string
  onMinTotalChange: (v: string) => void
  minIncrease4h: string
  onMinIncrease4hChange: (v: string) => void
  minVolume24h: string
  onMinVolume24hChange: (v: string) => void
  date: Date | undefined
  onDateChange: (v: Date | undefined) => void
  hour: string
  onHourChange: (v: string) => void
}

const TrendSignal4HFilters: React.FC<TrendSignal4HFiltersProps> = ({
  symbol,
  onSymbolChange,
  minConsecutive,
  onMinConsecutiveChange,
  minTotal,
  onMinTotalChange,
  minIncrease4h,
  onMinIncrease4hChange,
  minVolume24h,
  onMinVolume24hChange,
  date,
  onDateChange,
  hour,
  onHourChange,
}) => {
  return (
    <div className="flex flex-wrap gap-4 mt-6 mb-6 items-end">
      <div className="flex flex-col gap-1">
        <DatePicker value={date} onChange={onDateChange} />
      </div>
      <HourSelect4H hour={hour} onHourChange={onHourChange} />
      <div className="flex flex-col gap-1">
        <Input id="symbol" value={symbol} onChange={e => onSymbolChange(e.target.value)} placeholder="交易对" className="w-36 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <div className="flex gap-1">
          <Select value={minConsecutive?.split(',')[0] || '=='} onValueChange={op => onMinConsecutiveChange(op + ',' + (minConsecutive?.split(',')[1] || '3'))}>
            <SelectTrigger className="w-16">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="==">==</SelectItem>
              <SelectItem value=">=">&gt;=</SelectItem>
              <SelectItem value=">">&gt;</SelectItem>
              <SelectItem value="<=">&lt;=</SelectItem>
              <SelectItem value="<">&lt;</SelectItem>
            </SelectContent>
          </Select>
          <Input id="min-consecutive" type="number" value={minConsecutive?.split(',')[1] || '3'} onChange={e => onMinConsecutiveChange((minConsecutive?.split(',')[0] || '==') + ',' + e.target.value)} placeholder="连续次数" className="w-16 placeholder:text-muted-foreground" />
        </div>
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-total" type="number" value={minTotal} onChange={e => onMinTotalChange(e.target.value)} placeholder="总次数≥" className="w-28 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-increase4h" type="number" value={minIncrease4h} onChange={e => onMinIncrease4hChange(e.target.value)} placeholder="4h涨幅≥" className="w-28 placeholder:text-muted-foreground" />
      </div>
      <div className="flex flex-col gap-1">
        <Input id="min-volume24h" type="number" value={minVolume24h} onChange={e => onMinVolume24hChange(e.target.value)} placeholder="24h成交量≥" className="w-32 placeholder:text-muted-foreground" />
      </div>
    </div>
  )
}

export default TrendSignal4HFilters;
