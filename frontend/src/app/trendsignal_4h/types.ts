// trendsignal_4h 类型定义，保持和1h一致
export interface TrendSignal4H {
  id: number;
  symbol: string;
  timestamp: string;
  consecutive_ups_count: number;
  total_consecutive_downs: number;
  consecutive_3_and_12h_1pct_count: number;
  rank_in_12h_1pct: number;
  rank_4h_market: number;
  rank_12h_market: number;
  rank_24h_market: number;
  rank_48h_market: number;
  avg_increase_4h: number;
  avg_increase_12h: number;
  amplitude_4h: number;
  drawdown_4h: number;
  increase_4h: number;
  increase_12h: number;
  increase_24h: number;
  increase_48h: number;
  k1_increase: number;
  k2_increase: number;
  k3_increase: number;
  volume_24h: number;
  close_low_ratio: number;
  up_pairs_ratio: number;
}


export type TrendSignal4HItem = keyof TrendSignal4H;
export type TrendSignal4HItemValue = string | number;
