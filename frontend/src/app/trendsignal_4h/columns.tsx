import type { ColumnDef } from '@tanstack/react-table'
import type { TrendSignal4H } from './types'

export const TrendSignal4HColumns: ColumnDef<TrendSignal4H, any>[] = [
  { accessorKey: 'symbol', header: '币种' },
  { accessorKey: 'timestamp', header: '时间' },
  { accessorKey: 'consecutive_ups_count', header: '连续上涨数' },
  { accessorKey: 'total_consecutive_downs', header: '总连续下跌段数' },
  { accessorKey: 'consecutive_3_and_12h_1pct_count', header: '连续3且12h涨幅≥1累计' },
  { accessorKey: 'rank_in_12h_1pct', header: '连续3且12h涨幅≥1排名' },
  { accessorKey: 'rank_4h_market', header: '4h排名' },
  { accessorKey: 'rank_12h_market', header: '12h排名' },
  { accessorKey: 'rank_24h_market', header: '24h排名' },
  { accessorKey: 'avg_increase_4h', header: '4h均幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'avg_increase_12h', header: '12h均幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'increase_4h', header: '4h涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'increase_12h', header: '12h涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'increase_24h', header: '24h涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'increase_48h', header: '48h涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'k1_increase', header: 'K1涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'k2_increase', header: 'K2涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'k3_increase', header: 'K3涨幅', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'volume_24h', header: '24h成交量', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(0) : '-' },
  { accessorKey: 'up_pairs_ratio', header: '上涨占比', cell: ({ getValue }) => getValue() !== undefined ? (Number(getValue()) * 100).toFixed(1) + '%' : '-' },
];

