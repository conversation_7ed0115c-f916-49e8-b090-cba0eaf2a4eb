import type { TrendSignal1H } from "./columns"
import { api } from '@/lib/api-client';

export interface TrendSignal1HApiResult {
  data: TrendSignal1H[];
  total: number;
}

export interface TrendSignal1HFilters {
  page?: number
  page_size?: number
  date?: Date | undefined
  symbol?: string
  minConsecutive?: string
  minTotal?: string
  minIncrease4h?: string
  minVolume24h?: string
  hour?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export async function fetchTrendSignal1H(filters: TrendSignal1HFilters = {}): Promise<TrendSignal1HApiResult> {
  // 准备API参数
  const apiParams: Record<string, string | number | boolean | undefined> = {};

  if (filters.page) apiParams.page = filters.page;
  if (filters.page_size) apiParams.page_size = filters.page_size;
  if (filters.date) apiParams.date = filters.date.toISOString().slice(0, 10);
  if (filters.symbol) apiParams.symbol = filters.symbol;

  // 支持操作符传递
  if (filters.minConsecutive) {
    const [op, value] = filters.minConsecutive.split(',');
    if (op && value) {
      apiParams.min_consecutive = value;
      apiParams.min_consecutive_op = op;
    } else {
      apiParams.min_consecutive = filters.minConsecutive;
    }
  }

  if (filters.minTotal) apiParams.min_total = filters.minTotal;
  if (filters.minIncrease4h) apiParams.min_increase_4h = filters.minIncrease4h;
  if (filters.minVolume24h) apiParams.min_volume24h = filters.minVolume24h;
  if (filters.hour !== undefined && filters.hour !== '') apiParams.hour = filters.hour;
  if (filters.sortBy) apiParams.sort_by = filters.sortBy;
  if (filters.sortOrder) apiParams.order = filters.sortOrder;

  // 使用API客户端发送请求
  try {
    const result = await api.get('/data/trendsignal/1h', { params: apiParams });

    if (Array.isArray(result)) {
      // 后端直接返回数组，total为数组长度
      return { data: result, total: result.length };
    }

    // 标准结构
    return { data: result.data, total: result.total };
  } catch (error) {
    console.error('获取趋势信号数据失败:', error);
    throw new Error('获取趋势信号数据失败');
  }
}
