'use client'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { AppSidebar } from '@/components/app-sidebar'
import { Separator } from '@/components/ui/separator'
import { DataTable } from '@/components/ui/data-table'
import { columns } from './columns'
import { TrendSignal1HFilters } from './filters'
import { Alert } from '@/components/ui/alert'


import React, { useEffect, useState } from 'react'
import type { TrendSignal1H } from './columns'
import { fetchTrendSignal1H } from './api'

export default function TrendSignal1HPage() {
  // 筛选器状态
  const [date, setDate] = useState<Date | undefined>(undefined)
  const [symbol, setSymbol] = useState('')
  const [minConsecutive, setMinConsecutive] = useState('==,3')
  const [minTotal, setMinTotal] = useState('')
  const [minIncrease4h, setMinIncrease4h] = useState('')
  const [minVolume24h, setMinVolume24h] = useState('')
  const [data, setData] = useState<TrendSignal1H[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hour, setHour] = useState('')
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const fetchPage = React.useCallback((page: number, pageSize: number) => {
    setLoading(true)
    fetchTrendSignal1H({
      page: page + 1,
      page_size: pageSize,
      date,
      symbol,
      minConsecutive,
      minTotal,
      minIncrease4h,
      minVolume24h,
      hour,
      sortBy,
      sortOrder,
    })
      .then(res => {
        setData(res.data)
        setTotal(res.total)
      })
      .catch(e => setError(e.message || '数据加载失败'))
      .finally(() => setLoading(false))
  }, [date, symbol, minConsecutive, minTotal, minIncrease4h, minVolume24h, hour, sortBy, sortOrder])

  useEffect(() => {
    fetchPage(pageIndex, pageSize)
  }, [pageIndex, pageSize, fetchPage])

  // 修正筛选器：如果操作符或数值为空，自动补齐默认值
  useEffect(() => {
    if (!minConsecutive || !minConsecutive.includes(',')) {
      setMinConsecutive('==,3')
    } else {
      const [op, val] = minConsecutive.split(',')
      if (!op) setMinConsecutive('==,' + (val || '3'))
      if (!val) setMinConsecutive((op || '==') + ',3')
    }
  }, [minConsecutive])

  return (
    <SidebarProvider>
      <div className="flex h-screen w-screen">
        <AppSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <SidebarInset className="flex flex-col h-full">
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbPage>Trendsignal 1H</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </header>
            <main className="flex-1 flex flex-col min-w-0">
              <div className="flex-1 flex flex-col min-w-0">
                {error && (
                  <div className="fixed left-1/2 bottom-12 z-50 -translate-x-1/2 w-full max-w-md animate-in fade-in-0 slide-in-from-bottom-10">
                    <Alert variant="destructive" title="获取趋势信号数据失败">
                      {error}
                    </Alert>
                  </div>
                )}
                <div className="w-full flex-1 relative z-20 overflow-x-auto min-w-0">
                  <div className="px-4">
                    <TrendSignal1HFilters
                      date={date}
                      onDateChange={setDate}
                      symbol={symbol}
                      onSymbolChange={setSymbol}
                      minConsecutive={minConsecutive}
                      onMinConsecutiveChange={setMinConsecutive}
                      minTotal={minTotal}
                      onMinTotalChange={setMinTotal}
                      minIncrease4h={minIncrease4h}
                      onMinIncrease4hChange={setMinIncrease4h}
                      minVolume24h={minVolume24h}
                      onMinVolume24hChange={setMinVolume24h}
                      hour={hour}
                      onHourChange={setHour}
                    />
                    <DataTable
                      columns={columns}
                      data={data}
                      pageIndex={pageIndex}
                      pageSize={pageSize}
                      total={total}
                      onPageChange={setPageIndex}
                      onPageSizeChange={setPageSize}
                      sortBy={sortBy}
                      sortOrder={sortOrder}
                      onSortChange={(sortBy, sortOrder) => {
                        setSortBy(sortBy);
                        setSortOrder(sortOrder);
                        setPageIndex(0);
                      }}
                    />
                  </div>
                </div>
                {loading && <div className="text-center p-4">加载中...</div>}
              </div>
            </main>
          </SidebarInset>
        </div>
      </div>
    </SidebarProvider>
  )
}
