import { ColumnDef } from "@tanstack/react-table"

export type TrendSignal1H = {
  id: number;
  symbol: string;
  timestamp: string;

  consecutive_ups_count: number;
  total_consecutive_downs: number;
  consecutive_3_and_3h_1pct_count: number;
  rank_in_3h_1pct: number;
  rank_1h_market: number;
  rank_3h_market: number;
  rank_4h_market: number;
  rank_6h_market: number;
  avg_increase_1h: number;
  avg_increase_4h: number;
  avg_increase_12h: number;
  amplitude_1h: number;
  drawdown_1h: number;
  increase_1h: number;
  increase_3h: number;
  increase_4h: number;
  increase_6h: number;
  increase_12h: number;
  k1_increase: number;
  k2_increase: number;
  k3_increase: number;
  volume_24h: number;
  close_low_ratio: number;
  up_pairs_ratio: number;
};

export const columns: ColumnDef<TrendSignal1H>[] = [
  { accessorKey: "symbol", header: "币种" },
  { accessorKey: "timestamp", header: "北京时间" },
  { accessorKey: "consecutive_ups_count", header: "连续上涨数" },
  { accessorKey: "total_consecutive_downs", header: "总连续下跌段数" },
  { accessorKey: "consecutive_3_and_3h_1pct_count", header: "连续3且3h涨幅≥1累计" },
  { accessorKey: "rank_in_3h_1pct", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>连续3且3h涨幅≥1排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: "rank_1h_market", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>1h排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: "rank_3h_market", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>3h排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: "rank_4h_market", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>4h排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: "avg_increase_1h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>1h均幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "avg_increase_4h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>4h均幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "avg_increase_12h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>12h均幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "increase_1h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>1h涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "increase_3h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>3h涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "increase_4h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>4h涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "increase_6h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>6h涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "increase_12h", header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>12h涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-", enableSorting: true },
  { accessorKey: "k1_increase", header: "K1涨幅", cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-" },
  { accessorKey: "k2_increase", header: "K2涨幅", cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-" },
  { accessorKey: "k3_increase", header: "K3涨幅", cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : "-" },
  { accessorKey: "volume_24h", header: "24h成交量", cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(0) : "-" },
  { accessorKey: "up_pairs_ratio", header: "上涨占比", cell: ({ getValue }) => getValue() !== undefined ? (Number(getValue()) * 100).toFixed(1) + '%' : "-" },
];
