import type { ColumnDef } from "@tanstack/react-table";

export type TrendSignal1D = {
  id: number;
  symbol: string;
  timestamp: string;
  consecutive_ups_count: number;
  total_consecutive_downs: number;
  consecutive_3_and_3d_1pct_count: number;
  rank_in_3d_1pct: number;
  rank_1d_market: number;
  rank_3d_market: number;
  rank_7d_market: number;
  avg_increase_1d: number;
  avg_increase_4d: number;
  avg_increase_14d: number;
  amplitude_1d: number;
  drawdown_1d: number;
  increase_1d: number;
  increase_3d: number;
  increase_4d: number;
  increase_7d: number;
  increase_14d: number;
  k1_increase: number;
  k2_increase: number;
  k3_increase: number;
  volume_24h: number;
  close_low_ratio: number;
  up_pairs_ratio: number;
};

export const TrendSignal1DColumns: ColumnDef<TrendSignal1D, any>[] = [
  { accessorKey: 'symbol', header: '币种' },
  { accessorKey: 'timestamp', header: '时间' },
  { accessorKey: 'consecutive_ups_count', header: '连续上涨数' },
  { accessorKey: 'total_consecutive_downs', header: '总连续下跌段数' },
  { accessorKey: 'consecutive_3_and_3d_1pct_count', header: '连续3且3d涨幅≥1累计' },
  { accessorKey: 'rank_in_3d_1pct', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>连续3且3d涨幅≥1排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: 'rank_1d_market', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>1d排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: 'rank_3d_market', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>3d排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: 'rank_7d_market', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>7d排名
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), enableSorting: true },
  { accessorKey: 'avg_increase_1d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>1d均幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'avg_increase_4d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>4d均幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'avg_increase_14d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>14d均幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'increase_1d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>1d涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'increase_3d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>3d涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'increase_4d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>4d涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'increase_7d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>7d涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'increase_14d', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>14d涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'k1_increase', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>K1涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'k2_increase', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>K2涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'k3_increase', header: ({ column }) => (
    <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>K3涨幅
      <span className={column.getIsSorted() ? "text-primary" : "text-muted-foreground/70"}>
        {column.getIsSorted() === "asc" && "↑"}
        {column.getIsSorted() === "desc" && "↓"}
        {!column.getIsSorted() && <span style={{fontSize:'13px', letterSpacing: '-2px', display: 'inline-block'}}>↑↓</span>}
      </span>
    </div>
  ), cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-', enableSorting: true },
  { accessorKey: 'volume_24h', header: '24h成交量', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(0) : '-' },
  { accessorKey: 'close_low_ratio', header: '收盘/最低比', cell: ({ getValue }) => getValue() !== undefined ? Number(getValue()).toFixed(2) : '-' },
  { accessorKey: 'up_pairs_ratio', header: '上涨占比', cell: ({ getValue }) => getValue() !== undefined ? (Number(getValue()) * 100).toFixed(1) + '%' : '-' },
];
