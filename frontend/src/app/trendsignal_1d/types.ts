// trendsignal_1d 类型定义，保持和1h一致
export interface TrendSignal1D {
  id: number;
  symbol: string;
  timestamp: string;
  consecutive_ups_count: number;
  total_consecutive_downs: number;
  consecutive_3_and_3d_1pct_count: number;
  rank_in_3d_1pct: number;
  rank_1d_market: number;
  rank_3d_market: number;
  rank_7d_market: number;
  avg_increase_1d: number;
  avg_increase_4d: number;
  avg_increase_14d: number;
  amplitude_1d: number;
  drawdown_1d: number;
  increase_1d: number;
  increase_3d: number;
  increase_4d: number;
  increase_7d: number;
  increase_14d: number;
  k1_increase: number;
  k2_increase: number;
  k3_increase: number;
  volume_7d: number;
  close_low_ratio: number;
  up_pairs_ratio: number;
}


export type TrendSignal1DItem = keyof TrendSignal1D;
export type TrendSignal1DItemValue = string | number;
