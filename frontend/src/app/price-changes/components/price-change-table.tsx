"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { CryptoPriceChange } from '../types';

interface PriceChangeTableProps {
  title: string;
  data: CryptoPriceChange[];
  type: 'gainers' | 'losers';
}

export function PriceChangeTable({ title, data, type }: PriceChangeTableProps) {
  const formatPrice = (price: number) => {
    if (price >= 1) {
      return price.toFixed(4);
    } else if (price >= 0.01) {
      return price.toFixed(6);
    } else {
      return price.toFixed(8);
    }
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) {
      return `${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `${(volume / 1e6).toFixed(2)}M`;
    } else if (volume >= 1e3) {
      return `${(volume / 1e3).toFixed(2)}K`;
    }
    return volume.toFixed(2);
  };

  const getChangeColor = (change: number) => {
    if (change > 0) {
      return "text-green-600 dark:text-green-400";
    } else if (change < 0) {
      return "text-red-600 dark:text-red-400";
    }
    return "text-gray-600 dark:text-gray-400";
  };

  const getChangePrefix = (change: number) => {
    return change > 0 ? '+' : '';
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">
          {title}
          <span className="ml-2 text-sm font-normal text-muted-foreground">
            ({data.length} 个)
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="max-h-[600px] overflow-y-auto">
          <Table>
            <TableHeader className="sticky top-0 bg-background">
              <TableRow>
                <TableHead className="w-[100px]">排名</TableHead>
                <TableHead>币种</TableHead>
                <TableHead className="text-right">当前价格</TableHead>
                <TableHead className="text-right">涨跌幅</TableHead>
                <TableHead className="text-right">24h成交量</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.length > 0 ? (
                data.map((item, index) => (
                  <TableRow key={item.symbol} className="hover:bg-muted/50">
                    <TableCell className="font-medium">
                      {index + 1}
                    </TableCell>
                    <TableCell className="font-mono font-medium">
                      {item.symbol}
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      ${formatPrice(item.current_price)}
                    </TableCell>
                    <TableCell className={`text-right font-mono font-semibold ${getChangeColor(item.change_percentage)}`}>
                      {getChangePrefix(item.change_percentage)}{item.change_percentage.toFixed(2)}%
                    </TableCell>
                    <TableCell className="text-right font-mono text-sm">
                      ${formatVolume(item.quote_volume_24h)}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center text-muted-foreground">
                    暂无数据
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
