"use client";

import { useState, useEffect, useCallback } from 'react';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { AppSidebar } from '@/components/app-sidebar';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

import { TimeframeTabs } from './components/timeframe-tabs';
import { PriceChangeTable } from './components/price-change-table';
import { AutoRefreshIndicator } from './components/auto-refresh-indicator';
import { fetchPriceChanges } from './api';
import type { PriceChangesResponse, TimeframeKey } from './types';

const REFRESH_INTERVAL = 180; // 3 minutes in seconds

export default function PriceChangesPage() {
  const [activeTimeframe, setActiveTimeframe] = useState<TimeframeKey>('1h');
  const [data, setData] = useState<PriceChangesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<number | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadData = useCallback(async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setIsRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await fetchPriceChanges();
      setData(response);
      setLastUpdated(Date.now());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取数据失败';
      setError(errorMessage);
      console.error('获取价格变化数据失败:', err);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  // Initial data load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Auto refresh
  useEffect(() => {
    const interval = setInterval(() => {
      loadData(true);
    }, REFRESH_INTERVAL * 1000);

    return () => clearInterval(interval);
  }, [loadData]);

  const handleManualRefresh = () => {
    loadData(true);
  };

  const currentData = data?.data?.[activeTimeframe];

  if (loading && !data) {
    return (
      <SidebarProvider>
        <div className="flex h-screen w-screen">
          <AppSidebar />
          <div className="flex-1 flex flex-col min-w-0">
            <SidebarInset className="flex flex-col h-full">
              <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                <div className="flex items-center gap-2 px-4">
                  <SidebarTrigger className="-ml-1" />
                  <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                  <Breadcrumb>
                    <BreadcrumbList>
                      <BreadcrumbItem>
                        <BreadcrumbPage>加密货币价格变化</BreadcrumbPage>
                      </BreadcrumbItem>
                    </BreadcrumbList>
                  </Breadcrumb>
                </div>
              </header>
              <main className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">正在加载数据...</p>
                </div>
              </main>
            </SidebarInset>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-screen">
        <AppSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <SidebarInset className="flex flex-col h-full">
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbPage>加密货币价格变化</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </header>

            <main className="flex-1 flex flex-col min-w-0 p-4">
              {/* Auto refresh indicator */}
              <div className="mb-4 flex justify-between items-center">
                <div>
                  <h1 className="text-2xl font-bold">加密货币价格变化追踪</h1>
                  <p className="text-muted-foreground">实时追踪加密货币在不同时间周期的价格变化</p>
                </div>
                <AutoRefreshIndicator
                  isRefreshing={isRefreshing}
                  lastUpdated={lastUpdated}
                  onManualRefresh={handleManualRefresh}
                  refreshInterval={REFRESH_INTERVAL}
                />
              </div>

              {/* Error display */}
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Timeframe tabs */}
              <div className="mb-6">
                <TimeframeTabs
                  activeTimeframe={activeTimeframe}
                  onTimeframeChange={setActiveTimeframe}
                />
              </div>

              {/* Data tables */}
              {currentData && (
                <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-0">
                  <PriceChangeTable
                    title="涨幅榜"
                    data={currentData.gainers}
                    type="gainers"
                  />
                  <PriceChangeTable
                    title="跌幅榜"
                    data={currentData.losers}
                    type="losers"
                  />
                </div>
              )}

              {/* Data summary */}
              {data && (
                <div className="mt-4 text-center text-sm text-muted-foreground">
                  数据来源: 币安期货 | 总计 {data.total_symbols} 个交易对 | 
                  更新时间: {lastUpdated ? new Date(lastUpdated).toLocaleString('zh-CN') : '未知'}
                </div>
              )}
            </main>
          </SidebarInset>
        </div>
      </div>
    </SidebarProvider>
  );
}
