// TypeScript interfaces for price changes data

export interface CryptoPriceChange {
  symbol: string;
  current_price: number;
  change_percentage: number;
  volume_24h: number;
  quote_volume_24h: number;
}

export interface TimeframeData {
  gainers: CryptoPriceChange[];
  losers: CryptoPriceChange[];
}

export interface PriceChangesResponse {
  success: boolean;
  data: {
    '15m': TimeframeData;
    '1h': TimeframeData;
    '4h': TimeframeData;
    '6h': TimeframeData;
  };
  timestamp: number;
  total_symbols: number;
}

export type TimeframeKey = '15m' | '1h' | '4h' | '6h';

export interface TimeframeTab {
  key: TimeframeKey;
  label: string;
  description: string;
}
