// API配置文件
// 根据环境变量或默认值设置API基础URL

// 获取API基础URL
// 在生产环境中，API请求将通过Nginx代理转发到后端
// 在开发环境中，使用localhost:8000直接访问后端
export const getApiBaseUrl = (): string => {
  // 如果定义了环境变量，则使用环境变量
  if (process.env.NEXT_PUBLIC_API_BASE_URL) {
    return process.env.NEXT_PUBLIC_API_BASE_URL;
  }
  
  // 默认使用相对路径，通过Nginx代理转发
  // 这样在本地和服务器环境都可以使用相同的代码
  return '/api';
};

// 构建完整的API URL
export const buildApiUrl = (path: string): string => {
  const baseUrl = getApiBaseUrl();
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
};
