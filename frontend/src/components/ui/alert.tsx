import * as React from "react"
import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "destructive"
  title?: string
}

export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className, variant = "default", title, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="alert"
        className={cn(
          "relative w-full rounded-lg border p-4 text-sm",
          variant === "destructive"
            ? "border-destructive/50 text-destructive"
            : "border-muted bg-muted text-muted-foreground",
          className
        )}
        {...props}
      >
        <div className="flex items-start gap-2">
          <AlertCircle className={cn("mt-0.5 size-4 flex-shrink-0", variant === "destructive" ? "text-destructive" : "text-muted-foreground")} />
          <div>
            {title && <div className="font-medium mb-1">{title}</div>}
            {children}
          </div>
        </div>
      </div>
    )
  }
)
Alert.displayName = "Alert"
