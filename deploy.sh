#!/bin/bash
# K-line Signal 部署脚本

# 确保脚本在错误时退出
set -e

# 项目根目录
PROJECT_ROOT=$(pwd)

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# 检查是否安装了必要的软件
check_requirements() {
    info "检查必要的软件..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        error "未安装 Python3，请先安装"
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        error "未安装 Node.js，请先安装"
    fi
    
    # 检查 Nginx
    if ! command -v nginx &> /dev/null; then
        error "未安装 Nginx，请先安装"
    fi
    
    info "所有必要的软件已安装"
}

# 安装后端依赖
setup_backend() {
    info "设置后端环境..."
    
    cd "$PROJECT_ROOT"
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    pip install -r requirements.txt
    
    info "后端环境设置完成"
}

# 构建前端并配置 systemd 服务
build_frontend() {
    info "构建前端..."
    cd "$PROJECT_ROOT/frontend"
    
    # 安装依赖
    npm install
    
    # 构建生产版本
    npm run build
    
    info "前端构建完成"

    info "配置 Next.js 前端 systemd 服务..."
    cat > /tmp/kline-frontend.service << EOF
[Unit]
Description=KLine Frontend (Next.js)
After=network.target

[Service]
WorkingDirectory=$PROJECT_ROOT/frontend
ExecStart=$(which npm) run start
Restart=always
User=$(whoami)

[Install]
WantedBy=multi-user.target
EOF
    sudo cp /tmp/kline-frontend.service /etc/systemd/system/kline-frontend.service
    sudo systemctl daemon-reload
    sudo systemctl enable kline-frontend.service
    sudo systemctl restart kline-frontend.service
    info "前端 systemd 服务已启动"
}

# 配置 Nginx（反向代理 Next.js 3000 端口）
setup_nginx() {
    info "配置 Nginx..."
    NGINX_CONF_PATH="/etc/nginx/conf.d/kline-signal.conf"
    sudo tee "$NGINX_CONF_PATH" > /dev/null << EOF
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 可选：如有 API 路由
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
    sudo nginx -t || error "Nginx 配置测试失败"
    sudo systemctl reload nginx
    info "Nginx 配置完成"
}

# 设置系统服务
setup_service() {
    info "设置系统服务..."
    
    # 系统服务文件路径
    SERVICE_FILE="/etc/systemd/system/kline-backend.service"
    
    # 创建服务文件
    cat > /tmp/kline-backend.service << EOF
[Unit]
Description=KLine Backend Service
After=network.target

[Service]
WorkingDirectory=$PROJECT_ROOT
ExecStart=$PROJECT_ROOT/venv/bin/python3 -m backend.main
Restart=always
User=$(whoami)

[Install]
WantedBy=multi-user.target
EOF
    
    # 复制服务文件
    sudo cp /tmp/kline-backend.service "$SERVICE_FILE"
    
    # 重新加载 systemd 配置
    sudo systemctl daemon-reload
    
    # 启用并启动服务
    sudo systemctl enable kline-backend.service
    sudo systemctl restart kline-backend.service
    
    info "系统服务设置完成"
}

# 主函数
main() {
    info "开始部署 K-line Signal 项目..."
    check_requirements
    setup_backend
    build_frontend
    setup_nginx
    setup_service
    info "部署完成！"
    info "如遇页面无法访问，请检查 systemctl status kline-frontend.service 和 kline-backend.service 日志"
    info "如遇服务端口占用高，请用 ps aux --sort=-%mem | head -n 20 查看高占用进程"
    info "如需查看前端日志：sudo journalctl -u kline-frontend.service -n 100 --no-pager"
    info "如需查看后端日志：sudo journalctl -u kline-backend.service -n 100 --no-pager"
    info "可以通过 http://<服务器公网IP> 访问应用"
}

# 执行主函数
main
