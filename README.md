# K-line Signal 项目

K-line Signal 是一个基于 FastAPI + Next.js 的加密货币K线信号分析平台，支持前后端分离、JWT登录认证、现代化UI与灵活的扩展能力。

---

## 技术栈
- **后端**：FastAPI、SQLAlchemy、PostgreSQL、python-jose、passlib、pydantic-settings
- **前端**：Next.js (App Router)、React、shadcn/ui、TypeScript

---

## 目录结构
```
k_line_signal/
├── backend/        # FastAPI后端服务
│   ├── api/
│   ├── config/
│   ├── database/
│   ├── main.py
│   └── ...
├── frontend/      # Next.js前端项目
│   ├── src/app/
│   ├── src/components/
│   └── ...
├── requirements.txt
└── README.md
```

---

## 快速开始

### 方式一：使用开发脚本（推荐）

使用提供的开发脚本可以一键启动前后端服务：

```bash
# 确保脚本有执行权限
chmod +x start-dev.sh

# 启动开发环境
./start-dev.sh

# 访问 http://localhost:3000
```

### 方式二：手动启动

#### 1. 后端启动
```bash
cd backend
python -m venv venv
source venv/bin/activate
pip install -r ../requirements.txt
# 初始化数据库（如有）
python3 -m backend.database.init_db
uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. 前端启动
```bash
cd frontend
npm install
npm run dev
# 访问 http://localhost:3000
```

### 方式三：生产环境部署（使用Nginx）

使用提供的部署脚本可以一键部署到生产环境：

```bash
# 确保脚本有执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh

# 访问 http://your-server-ip 或 http://your-domain
```

---

## 重要环境变量
请在 backend/config/.env 文件中配置敏感信息，例如：
```
SECRET_KEY=your-secret-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin
DATABASE_URL=postgresql://postgres:yourpassword@localhost:5432/COIN_KLINE
```

---

## 主要功能
- 用户登录（JWT认证，密码加密）
- 登录成功后跳转 Dashboard 页面
- 前后端分离，接口安全
- 现代化 UI（shadcn/ui）
- 多周期趋势信号表格（支持15分钟、4小时、1天等多种K线周期）
- 各周期表格字段统一、类型安全，render函数支持数值格式化
- 前端表格组件、筛选器组件重构，props类型健壮，支持灵活扩展
- 支持币种、名称、涨幅、成交量等多字段展示与筛选
- 代码已彻底清理冗余导出，lint和类型错误已修复
- 支持 Postgres 17 数据库，环境变量配置灵活
- 前后端开发环境与虚拟环境启动流程标准化
- 代码结构清晰，便于二次开发和功能扩展

---

## Nginx配置说明

本项目使用Nginx作为反向代理，实现以下功能：

1. **统一入口**：通过Nginx代理前端和后端请求，使用同一个域名/IP访问
2. **API转发**：将`/api`开头的请求转发到后端服务
3. **静态资源缓存**：对静态资源（如图片、CSS、JS）进行缓存优化

### Nginx配置文件

配置文件位于`nginx/kline-signal.conf`，主要包含以下部分：

```nginx
# 前端路由
location / {
    try_files $uri $uri/ /index.html;
}

# API请求代理到后端服务
location /api/ {
    rewrite ^/api/(.*) /$1 break;
    proxy_pass http://localhost:8000;
    # 其他代理设置...
}
```

### 本地与服务器环境统一

通过Nginx配置，实现了本地开发环境和服务器生产环境使用相同的代码：

1. **开发环境**：使用Next.js的rewrites功能模拟Nginx代理
2. **生产环境**：使用实际的Nginx服务器进行代理

前端代码中的API调用统一使用相对路径（`/api/...`），不再硬编码服务器地址。

---

## 常见问题
- **后端端口占用**：`lsof -ti:8000 | xargs kill -9`
- **依赖缺失报错**：`pip install -r requirements.txt` 补齐依赖
- **表单报错需安装 python-multipart**：`pip install python-multipart`
- **数据库相关**：确保 PostgreSQL 已启动，且 .env 配置正确
- **Nginx配置错误**：检查`nginx -t`输出，确保配置文件语法正确
- **权限问题**：确保Nginx有权限访问前端构建文件和后端服务

---

## 贡献与开发
如需扩展注册、用户管理、信号推送等功能，欢迎提交 issue 或 PR。

---

## 联系方式
如有问题请联系项目维护者。
