# 激活虚拟环境
source venv/bin/activate

# 启动前端开发服务器
cd frontend
npm run dev
source venv/bin/activate && cd frontend && npm run dev

# 启动 FastAPI 服务
    source venv/bin/activate && python3 -m backend.main

    uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000


    cd /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/k_line_signal
    source venv/bin/activate
    python3 -m backend.database.init_db

# 查询数据库有哪些表
/opt/homebrew/opt/postgresql@17/bin/psql -U postgres -d COIN_KLINE -c "\dt"


# 查询交易对按照天分布
/opt/homebrew/opt/postgresql@17/bin/psql -U postgres -d COIN_KLINE -c "SELECT DATE(timestamp) as day, COUNT(*) FROM ra
wkline_1h WHERE symbol LIKE 'BTC/%' OR symbol LIKE 'BTC:%' GROUP BY day ORDER BY day;"
