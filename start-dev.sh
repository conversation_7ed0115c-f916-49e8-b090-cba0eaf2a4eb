#!/bin/bash
# K-line Signal 本地开发环境启动脚本

# 确保脚本在错误时退出
set -e

# 项目根目录
PROJECT_ROOT=$(pwd)

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# 启动后端服务
start_backend() {
    info "启动后端服务..."
    
    cd "$PROJECT_ROOT"
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 启动后端服务
    python3 -m backend.main &
    
    # 保存后端进程ID
    BACKEND_PID=$!
    
    info "后端服务已启动，PID: $BACKEND_PID"
}

# 启动前端开发服务器
start_frontend() {
    info "启动前端开发服务器..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # 启动前端开发服务器
    npm run dev &
    
    # 保存前端进程ID
    FRONTEND_PID=$!
    
    info "前端开发服务器已启动，PID: $FRONTEND_PID"
}

# 清理函数
cleanup() {
    info "正在清理..."
    
    # 终止后端进程
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        info "后端服务已停止"
    fi
    
    # 终止前端进程
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        info "前端开发服务器已停止"
    fi
    
    exit 0
}

# 注册清理函数
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    info "启动 K-line Signal 开发环境..."
    
    start_backend
    start_frontend
    
    info "开发环境已启动"
    info "前端: http://localhost:3000"
    info "后端: http://localhost:8000"
    info "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    wait
}

# 执行主函数
main
