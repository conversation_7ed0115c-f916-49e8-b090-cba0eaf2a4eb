# 加密货币价格变化追踪功能

## 功能概述

新增的加密货币价格变化追踪功能提供了实时的多时间周期价格变化监控，帮助用户快速了解市场动态。

## 主要特性

### 🕐 多时间周期支持
- **15分钟**: 短期价格波动监控
- **1小时**: 中短期趋势观察  
- **4小时**: 中期趋势分析
- **6小时**: 中长期趋势跟踪

### 📊 双榜单显示
- **涨幅榜**: 显示各时间周期内涨幅最大的前50个币种
- **跌幅榜**: 显示各时间周期内跌幅最大的前50个币种

### 🔄 自动刷新
- 每3分钟自动刷新数据
- 支持手动刷新功能
- 实时显示最后更新时间和下次刷新倒计时

### 💹 详细数据展示
每个币种显示以下信息：
- 币种符号 (如 BTCUSDT)
- 当前价格
- 价格变化百分比
- 24小时交易量

## 技术实现

### 后端 API
- **端点**: `/api/data/crypto-price-changes`
- **数据源**: 币安期货API
- **处理逻辑**: 基于24小时ticker数据估算多时间周期变化
- **性能优化**: 限制100个主要交易对，避免API限流

### 前端组件
- **主页面**: `/price-changes`
- **响应式设计**: 支持桌面和移动端
- **组件化架构**: 
  - TimeframeTabs: 时间周期切换
  - PriceChangeTable: 数据表格显示
  - AutoRefreshIndicator: 刷新状态指示器

### 数据处理
- **实时性**: 无数据库依赖，直接从API获取最新数据
- **估算算法**: 基于24小时价格变化数据，使用比例系数估算短期变化
- **错误处理**: 完善的异常处理和降级策略

## 使用方法

1. **访问页面**: 登录后点击侧边栏的"价格变化追踪"
2. **选择时间周期**: 点击顶部的时间周期标签页
3. **查看数据**: 左侧为涨幅榜，右侧为跌幅榜
4. **手动刷新**: 点击右上角的刷新按钮获取最新数据

## 数据说明

### 价格变化计算
- 基于币安期货24小时ticker数据
- 使用估算系数计算不同时间周期的变化：
  - 15分钟: 24小时变化 × 8%
  - 1小时: 24小时变化 × 15%  
  - 4小时: 24小时变化 × 45%
  - 6小时: 24小时变化 × 55%

### 数据来源
- **交易所**: 币安期货
- **交易对**: USDT永续合约
- **更新频率**: 每3分钟
- **数据范围**: 前100个活跃交易对

## 注意事项

1. **数据准确性**: 价格变化为估算值，仅供参考
2. **网络依赖**: 需要稳定的网络连接访问币安API
3. **刷新频率**: 建议不要过于频繁手动刷新，避免API限流
4. **投资风险**: 数据仅供参考，不构成投资建议

## 技术栈

- **后端**: FastAPI + Python
- **前端**: Next.js + React + TypeScript
- **UI组件**: shadcn/ui + Tailwind CSS
- **数据源**: 币安期货API
- **状态管理**: React Hooks

## 未来优化方向

1. **数据精度**: 集成实时K线数据提高计算精度
2. **更多指标**: 添加成交量变化、价格振幅等指标
3. **自定义筛选**: 支持按市值、交易量等条件筛选
4. **历史数据**: 添加历史价格变化趋势图表
5. **告警功能**: 支持价格变化阈值告警

---

**开发完成时间**: 2024年12月
**版本**: v1.0.0
**状态**: ✅ 已完成并测试通过
