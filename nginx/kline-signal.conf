server {
    listen 80;
    server_name localhost;  # 在生产环境中，替换为实际域名

    # 前端静态文件目录
    root /path/to/k_line_signal/frontend/.next;
    
    # 日志配置
    access_log /var/log/nginx/kline-signal-access.log;
    error_log /var/log/nginx/kline-signal-error.log;

    # 前端路由
    location / {
        # 尝试提供请求的文件，如果不存在则回退到index.html
        try_files $uri $uri/ /index.html;
    }

    # API请求代理到后端服务
    location /api/ {
        # 移除/api前缀
        rewrite ^/api/(.*) /$1 break;
        
        # 代理到后端服务
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态资源缓存设置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
